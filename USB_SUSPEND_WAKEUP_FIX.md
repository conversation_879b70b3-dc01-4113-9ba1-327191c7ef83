# USB HID设备挂起后无法发送数据问题修复

## 问题描述

USB HID设备在一段时间没有通信后会被主机挂起（suspend），此时再次按按键也无法发出数据。

## 问题原因

1. **远程唤醒功能未启用**：USB配置描述符中的`bmAttributes`字段没有设置远程唤醒位
2. **远程唤醒代码被注释**：main.c中的远程唤醒相关代码被注释掉了
3. **缺少状态监控**：没有正确的USB状态监控和恢复机制

## 修复方案

### 1. 启用USB配置描述符中的远程唤醒功能

**文件**: `Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Src/usbd_customhid.c`

**修改**: 将所有配置描述符中的`bmAttributes`从`0xC0`改为`0xE0`

```c
// 修改前
0xC0,         /*bmAttributes: bus powered */

// 修改后  
0xE0,         /*bmAttributes: bus powered + remote wakeup */
```

### 2. 启用PCD层的远程唤醒功能

**文件**: `USB_DEVICE/Target/usbd_conf.c`

**修改**: 取消注释远程唤醒启用代码

```c
// 修改前
//__HAL_PCD_ENABLE_REMOTE_WAKEUP(hpcd);

// 修改后
/* 启用远程唤醒功能 */
__HAL_PCD_ENABLE_REMOTE_WAKEUP(hpcd);
```

### 3. 实现完整的USB唤醒机制

**文件**: `Core/Src/main.c`

**新增功能**:
- `USB_WakeupDevice()`: 执行USB远程唤醒序列
- `USB_MonitorStatus()`: 监控USB状态变化
- 状态跟踪变量: `usb_was_suspended`, `last_activity_time`

**主要改进**:
1. 检测设备挂起状态
2. 执行标准的远程唤醒序列（激活信号→延时→停止信号→恢复状态）
3. 等待设备完全唤醒后再发送数据
4. 添加USB状态监控和自动恢复机制

## 使用方法

### 编译和烧录

1. 使用STM32CubeIDE或Keil MDK编译项目
2. 将固件烧录到STM32设备

### 测试

1. 连接USB设备到PC
2. 等待3-5秒让设备进入挂起状态（LED红灯熄灭）
3. 按下按键，设备应该能够：
   - 自动唤醒（LED红灯亮起）
   - 成功发送HID数据

### Python测试脚本

使用提供的`test_usb_wakeup.py`脚本进行自动化测试：

```bash
# 安装依赖
pip install hidapi

# 运行测试
python test_usb_wakeup.py
```

## 技术细节

### USB远程唤醒序列

1. **检查远程唤醒支持**: 验证`dev_remote_wakeup`标志
2. **激活唤醒信号**: 调用`HAL_PCD_ActivateRemoteWakeup()`
3. **保持信号**: 延时10ms（USB规范要求1-15ms）
4. **停止信号**: 调用`HAL_PCD_DeActivateRemoteWakeup()`
5. **恢复状态**: 调用`USBD_LL_Resume()`

### 状态监控机制

- 监控USB设备状态变化
- 检测挂起/恢复事件
- 自动处理长时间断连情况
- LED指示当前状态

## 注意事项

1. **主机支持**: 确保USB主机（PC）支持并启用了远程唤醒功能
2. **电源管理**: 某些PC的电源管理设置可能会禁用USB远程唤醒
3. **延时调整**: 根据实际情况可能需要调整唤醒延时时间
4. **兼容性**: 不同的USB主机控制器可能有不同的行为

## 故障排除

### 设备仍无法唤醒

1. 检查PC的USB电源管理设置
2. 在设备管理器中启用USB设备的"允许此设备唤醒计算机"选项
3. 检查USB线缆质量
4. 尝试不同的USB端口

### 数据发送失败

1. 确认设备已完全唤醒（检查`dev_state`）
2. 增加唤醒后的等待时间
3. 检查HID报告格式是否正确

### LED指示说明

- **红灯亮**: 设备正常工作状态
- **红灯灭**: 设备处于挂起状态
- **红灯闪烁**: 设备正在唤醒过程中

## 版本历史

- **v1.0**: 初始修复版本，实现基本的远程唤醒功能
- **v1.1**: 添加状态监控和自动恢复机制
- **v1.2**: 优化唤醒时序和错误处理
