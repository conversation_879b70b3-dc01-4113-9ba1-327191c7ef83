<html>
<body>
<pre>
<h1>�Vision Build Log</h1>
<h2>Tool Versions:</h2>
IDE-Version: ��Vision V5.42.0.0
Copyright (C) 2025 ARM Ltd and ARM Germany GmbH. All rights reserved.
License Information: T L, TT, LIC=KRMW3-ADW47-562Q2-9WFX2-17VG9-5BRGU
 
Tool Versions:
Toolchain:       MDK-ARM Plus  Version: 5.42.0.0
Toolchain Path:  C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin
C Compiler:      Armcc.exe V5.06 update 7 (build 960)
Assembler:       Armasm.exe V5.06 update 7 (build 960)
Linker/Locator:  ArmLink.exe V5.06 update 7 (build 960)
Library Manager: ArmAr.exe V5.06 update 7 (build 960)
Hex Converter:   FromElf.exe V5.06 update 7 (build 960)
CPU DLL:         SARMCM3.DLL V5.42.0.0
Dialog DLL:      DCM.DLL V1.17.5.0
Target DLL:      STLink\ST-LINKIII-KEIL_SWO.dll V3.3.0.0
Dialog DLL:      TCM.DLL V1.56.6.0
 
<h2>Project:</h2>
D:\Develop\Neewer\Pre-research\Resources\USB_HID\MDK-ARM\USB_HID.uvprojx
Project File Date:  08/01/2025

<h2>Output:</h2>
*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin'
Build target 'USB_HID'
assembling startup_stm32f103xb.s...
compiling stm32f1xx_hal_msp.c...
compiling stm32f1xx_it.c...
compiling usbd_custom_hid_if.c...
compiling gpio.c...
compiling usb_device.c...
compiling stm32f1xx_hal_pcd_ex.c...
compiling usbd_conf.c...
compiling usbd_desc.c...
compiling stm32f1xx_hal_pcd.c...
compiling stm32f1xx_hal_gpio_ex.c...
compiling main.c...
compiling stm32f1xx_hal_cortex.c...
compiling stm32f1xx_hal_dma.c...
compiling stm32f1xx_hal_gpio.c...
compiling stm32f1xx_hal_rcc_ex.c...
compiling stm32f1xx_hal.c...
compiling stm32f1xx_hal_rcc.c...
compiling stm32f1xx_hal_flash.c...
compiling stm32f1xx_hal_flash_ex.c...
compiling stm32f1xx_hal_pwr.c...
compiling stm32f1xx_ll_usb.c...
compiling stm32f1xx_hal_exti.c...
compiling system_stm32f1xx.c...
compiling usbd_core.c...
compiling usbd_ioreq.c...
compiling usbd_ctlreq.c...
compiling usbd_customhid.c...
linking...
Program Size: Code=12648 RO-data=304 RW-data=424 ZI-data=3672  
FromELF: creating hex file...
"USB_HID\USB_HID.axf" - 0 Error(s), 0 Warning(s).

<h2>Software Packages used:</h2>

Package Vendor: ARM
                https://www.keil.com/pack/ARM.CMSIS.6.1.0.pack
                ARM::CMSIS@6.1.0
                CMSIS (Common Microcontroller Software Interface Standard)
   * Component: CORE Version: 6.1.0

Package Vendor: Keil
                https://www.keil.com/pack/Keil.STM32F1xx_DFP.2.4.1.pack
                Keil::STM32F1xx_DFP@2.4.1
                STMicroelectronics STM32F1 Series Device Support, Drivers and Examples

<h2>Collection of Component include folders:</h2>
  ./RTE/_USB_HID
  C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include
  C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

<h2>Collection of Component Files used:</h2>

   * Component: ARM::CMSIS:CORE@6.1.0
      Include file:  CMSIS/Core/Include/tz_context.h
Build Time Elapsed:  00:00:07
</pre>
</body>
</html>
