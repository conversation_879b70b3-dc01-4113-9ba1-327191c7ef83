Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) for USB_LP_CAN1_RX0_IRQHandler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    main.o(i.HAL_GPIO_EXTI_Callback) refers to main.o(.data) for .data
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(i.USB_IO_rest) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.USB_IO_rest) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(i.USB_MonitorStatus) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.USB_MonitorStatus) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.USB_MonitorStatus) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop) for HAL_PCD_Stop
    main.o(i.USB_MonitorStatus) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.USB_MonitorStatus) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_Start) for HAL_PCD_Start
    main.o(i.USB_MonitorStatus) refers to usb_device.o(.bss) for hUsbDeviceFS
    main.o(i.USB_MonitorStatus) refers to main.o(.data) for .data
    main.o(i.USB_MonitorStatus) refers to usbd_conf.o(.bss) for hpcd_USB_FS
    main.o(i.USB_WakeupDevice) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_ActivateRemoteWakeup) for HAL_PCD_ActivateRemoteWakeup
    main.o(i.USB_WakeupDevice) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.USB_WakeupDevice) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_DeActivateRemoteWakeup) for HAL_PCD_DeActivateRemoteWakeup
    main.o(i.USB_WakeupDevice) refers to usbd_core.o(i.USBD_LL_Resume) for USBD_LL_Resume
    main.o(i.USB_WakeupDevice) refers to usb_device.o(.bss) for hUsbDeviceFS
    main.o(i.USB_WakeupDevice) refers to usbd_conf.o(.bss) for hpcd_USB_FS
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to main.o(i.USB_IO_rest) for USB_IO_rest
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usb_device.o(i.MX_USB_DEVICE_Init) for MX_USB_DEVICE_Init
    main.o(i.main) refers to main.o(i.USB_MonitorStatus) for USB_MonitorStatus
    main.o(i.main) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.main) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_SendReport) for USBD_CUSTOM_HID_SendReport
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.main) refers to main.o(i.USB_WakeupDevice) for USB_WakeupDevice
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to usb_device.o(.bss) for hUsbDeviceFS
    main.o(i.main) refers to main.o(.data) for .data
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f1xx_it.o(i.EXTI0_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) for HAL_PCD_IRQHandler
    stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to usbd_conf.o(.bss) for hpcd_USB_FS
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_core.o(i.USBD_Init) for USBD_Init
    usb_device.o(i.MX_USB_DEVICE_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_core.o(i.USBD_RegisterClass) for USBD_RegisterClass
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_RegisterInterface) for USBD_CUSTOM_HID_RegisterInterface
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_core.o(i.USBD_Start) for USBD_Start
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_desc.o(.data) for FS_Desc
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usb_device.o(.bss) for .bss
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_customhid.o(.data) for USBD_CUSTOM_HID
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_custom_hid_if.o(.data) for USBD_CustomHID_fops_FS
    usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) refers to usbd_desc.o(.bss) for .bss
    usbd_desc.o(i.USBD_FS_DeviceDescriptor) refers to usbd_desc.o(.data) for .data
    usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) refers to usbd_desc.o(.bss) for .bss
    usbd_desc.o(i.USBD_FS_LangIDStrDescriptor) refers to usbd_desc.o(.data) for .data
    usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) refers to usbd_desc.o(.bss) for .bss
    usbd_desc.o(i.USBD_FS_ProductStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_ProductStrDescriptor) refers to usbd_desc.o(.bss) for .bss
    usbd_desc.o(i.USBD_FS_SerialStrDescriptor) refers to usbd_desc.o(i.IntToUnicode) for IntToUnicode
    usbd_desc.o(i.USBD_FS_SerialStrDescriptor) refers to usbd_desc.o(.data) for .data
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_DeviceDescriptor) for USBD_FS_DeviceDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_LangIDStrDescriptor) for USBD_FS_LangIDStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) for USBD_FS_ManufacturerStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_ProductStrDescriptor) for USBD_FS_ProductStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_SerialStrDescriptor) for USBD_FS_SerialStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) for USBD_FS_ConfigStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) for USBD_FS_InterfaceStrDescriptor
    usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) refers to strncpy.o(.text) for strncpy
    usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) refers to usb_device.o(.bss) for hUsbDeviceFS
    usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) refers to main.o(.data) for usb_rx_buffer
    usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) refers to main.o(.data) for usb_rx_flag
    usbd_custom_hid_if.o(.data) refers to usbd_custom_hid_if.o(.data) for CUSTOM_HID_ReportDesc_FS
    usbd_custom_hid_if.o(.data) refers to usbd_custom_hid_if.o(i.CUSTOM_HID_Init_FS) for CUSTOM_HID_Init_FS
    usbd_custom_hid_if.o(.data) refers to usbd_custom_hid_if.o(i.CUSTOM_HID_DeInit_FS) for CUSTOM_HID_DeInit_FS
    usbd_custom_hid_if.o(.data) refers to usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) for CUSTOM_HID_OutEvent_FS
    usbd_conf.o(i.HAL_PCD_ConnectCallback) refers to usbd_core.o(i.USBD_LL_DevConnected) for USBD_LL_DevConnected
    usbd_conf.o(i.HAL_PCD_DataInStageCallback) refers to usbd_core.o(i.USBD_LL_DataInStage) for USBD_LL_DataInStage
    usbd_conf.o(i.HAL_PCD_DataOutStageCallback) refers to usbd_core.o(i.USBD_LL_DataOutStage) for USBD_LL_DataOutStage
    usbd_conf.o(i.HAL_PCD_DisconnectCallback) refers to usbd_core.o(i.USBD_LL_DevDisconnected) for USBD_LL_DevDisconnected
    usbd_conf.o(i.HAL_PCD_ISOINIncompleteCallback) refers to usbd_core.o(i.USBD_LL_IsoINIncomplete) for USBD_LL_IsoINIncomplete
    usbd_conf.o(i.HAL_PCD_ISOOUTIncompleteCallback) refers to usbd_core.o(i.USBD_LL_IsoOUTIncomplete) for USBD_LL_IsoOUTIncomplete
    usbd_conf.o(i.HAL_PCD_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usbd_conf.o(i.HAL_PCD_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usbd_conf.o(i.HAL_PCD_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usbd_conf.o(i.HAL_PCD_ResetCallback) refers to main.o(i.Error_Handler) for Error_Handler
    usbd_conf.o(i.HAL_PCD_ResetCallback) refers to usbd_core.o(i.USBD_LL_SetSpeed) for USBD_LL_SetSpeed
    usbd_conf.o(i.HAL_PCD_ResetCallback) refers to usbd_core.o(i.USBD_LL_Reset) for USBD_LL_Reset
    usbd_conf.o(i.HAL_PCD_ResumeCallback) refers to usbd_core.o(i.USBD_LL_Resume) for USBD_LL_Resume
    usbd_conf.o(i.HAL_PCD_SOFCallback) refers to usbd_core.o(i.USBD_LL_SOF) for USBD_LL_SOF
    usbd_conf.o(i.HAL_PCD_SetupStageCallback) refers to usbd_core.o(i.USBD_LL_SetupStage) for USBD_LL_SetupStage
    usbd_conf.o(i.HAL_PCD_SuspendCallback) refers to usbd_core.o(i.USBD_LL_Suspend) for USBD_LL_Suspend
    usbd_conf.o(i.USBD_LL_ClearStallEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall) for HAL_PCD_EP_ClrStall
    usbd_conf.o(i.USBD_LL_ClearStallEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_CloseEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Close) for HAL_PCD_EP_Close
    usbd_conf.o(i.USBD_LL_CloseEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_DeInit) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_DeInit) for HAL_PCD_DeInit
    usbd_conf.o(i.USBD_LL_DeInit) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_Delay) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    usbd_conf.o(i.USBD_LL_FlushEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Flush) for HAL_PCD_EP_Flush
    usbd_conf.o(i.USBD_LL_FlushEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_GetRxDataSize) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_GetRxCount) for HAL_PCD_EP_GetRxCount
    usbd_conf.o(i.USBD_LL_Init) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) for HAL_PCD_Init
    usbd_conf.o(i.USBD_LL_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usbd_conf.o(i.USBD_LL_Init) refers to stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_PMAConfig) for HAL_PCDEx_PMAConfig
    usbd_conf.o(i.USBD_LL_Init) refers to usbd_conf.o(.bss) for .bss
    usbd_conf.o(i.USBD_LL_OpenEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Open) for HAL_PCD_EP_Open
    usbd_conf.o(i.USBD_LL_OpenEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_PrepareReceive) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Receive) for HAL_PCD_EP_Receive
    usbd_conf.o(i.USBD_LL_PrepareReceive) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_SetUSBAddress) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress) for HAL_PCD_SetAddress
    usbd_conf.o(i.USBD_LL_SetUSBAddress) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_StallEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall) for HAL_PCD_EP_SetStall
    usbd_conf.o(i.USBD_LL_StallEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_Start) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_Start) for HAL_PCD_Start
    usbd_conf.o(i.USBD_LL_Start) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_Stop) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop) for HAL_PCD_Stop
    usbd_conf.o(i.USBD_LL_Stop) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_Transmit) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Transmit) for HAL_PCD_EP_Transmit
    usbd_conf.o(i.USBD_LL_Transmit) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_static_malloc) refers to usbd_conf.o(.bss) for .bss
    stm32f1xx_hal_pcd.o(i.HAL_PCD_ActivateRemoteWakeup) refers to stm32f1xx_ll_usb.o(i.USB_ActivateRemoteWakeup) for USB_ActivateRemoteWakeup
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DeActivateRemoteWakeup) refers to stm32f1xx_ll_usb.o(i.USB_DeActivateRemoteWakeup) for USB_DeActivateRemoteWakeup
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DeInit) refers to stm32f1xx_ll_usb.o(i.USB_StopDevice) for USB_StopDevice
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DeInit) refers to usbd_conf.o(i.HAL_PCD_MspDeInit) for HAL_PCD_MspDeInit
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DevConnect) refers to usbd_conf.o(i.HAL_PCDEx_SetConnectionState) for HAL_PCDEx_SetConnectionState
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DevConnect) refers to stm32f1xx_ll_usb.o(i.USB_DevConnect) for USB_DevConnect
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DevDisconnect) refers to usbd_conf.o(i.HAL_PCDEx_SetConnectionState) for HAL_PCDEx_SetConnectionState
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DevDisconnect) refers to stm32f1xx_ll_usb.o(i.USB_DevDisconnect) for USB_DevDisconnect
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Abort) refers to stm32f1xx_ll_usb.o(i.USB_EPStopXfer) for USB_EPStopXfer
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Close) refers to stm32f1xx_ll_usb.o(i.USB_DeactivateEndpoint) for USB_DeactivateEndpoint
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall) refers to stm32f1xx_ll_usb.o(i.USB_EPClearStall) for USB_EPClearStall
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Receive) refers to stm32f1xx_ll_usb.o(i.USB_ReadPMA) for USB_ReadPMA
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit) refers to usbd_conf.o(i.HAL_PCD_DataInStageCallback) for HAL_PCD_DataInStageCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit) refers to stm32f1xx_ll_usb.o(i.USB_WritePMA) for USB_WritePMA
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Flush) refers to stm32f1xx_ll_usb.o(i.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Flush) refers to stm32f1xx_ll_usb.o(i.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Open) refers to stm32f1xx_ll_usb.o(i.USB_ActivateEndpoint) for USB_ActivateEndpoint
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Receive) refers to stm32f1xx_ll_usb.o(i.USB_EPStartXfer) for USB_EPStartXfer
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall) refers to stm32f1xx_ll_usb.o(i.USB_EPSetStall) for USB_EPSetStall
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall) refers to stm32f1xx_ll_usb.o(i.USB_EP0_OutStart) for USB_EP0_OutStart
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Transmit) refers to stm32f1xx_ll_usb.o(i.USB_EPStartXfer) for USB_EPStartXfer
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f1xx_ll_usb.o(i.USB_ReadInterrupts) for USB_ReadInterrupts
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) for PCD_EP_ISR_Handler
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_ResetCallback) for HAL_PCD_ResetCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress) for HAL_PCD_SetAddress
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_ResumeCallback) for HAL_PCD_ResumeCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_SuspendCallback) for HAL_PCD_SuspendCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_SOFCallback) for HAL_PCD_SOFCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to usbd_conf.o(i.HAL_PCD_MspInit) for HAL_PCD_MspInit
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_CoreInit) for USB_CoreInit
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_SetCurrentMode) for USB_SetCurrentMode
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_DevInit) for USB_DevInit
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_DevDisconnect) for USB_DevDisconnect
    stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress) refers to stm32f1xx_ll_usb.o(i.USB_SetDevAddress) for USB_SetDevAddress
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Start) refers to stm32f1xx_ll_usb.o(i.USB_EnableGlobalInt) for USB_EnableGlobalInt
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Start) refers to usbd_conf.o(i.HAL_PCDEx_SetConnectionState) for HAL_PCDEx_SetConnectionState
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Start) refers to stm32f1xx_ll_usb.o(i.USB_DevConnect) for USB_DevConnect
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop) refers to stm32f1xx_ll_usb.o(i.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop) refers to usbd_conf.o(i.HAL_PCDEx_SetConnectionState) for HAL_PCDEx_SetConnectionState
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop) refers to stm32f1xx_ll_usb.o(i.USB_DevDisconnect) for USB_DevDisconnect
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to usbd_conf.o(i.HAL_PCD_DataInStageCallback) for HAL_PCD_DataInStageCallback
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to stm32f1xx_ll_usb.o(i.USB_ReadPMA) for USB_ReadPMA
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to usbd_conf.o(i.HAL_PCD_SetupStageCallback) for HAL_PCD_SetupStageCallback
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to usbd_conf.o(i.HAL_PCD_DataOutStageCallback) for HAL_PCD_DataOutStageCallback
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Receive) for HAL_PCD_EP_DB_Receive
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to stm32f1xx_ll_usb.o(i.USB_EPStartXfer) for USB_EPStartXfer
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit) for HAL_PCD_EP_DB_Transmit
    stm32f1xx_ll_usb.o(i.USB_EPStartXfer) refers to stm32f1xx_ll_usb.o(i.USB_WritePMA) for USB_WritePMA
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to main.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    usbd_core.o(i.USBD_DeInit) refers to usbd_conf.o(i.USBD_LL_Stop) for USBD_LL_Stop
    usbd_core.o(i.USBD_DeInit) refers to usbd_conf.o(i.USBD_LL_DeInit) for USBD_LL_DeInit
    usbd_core.o(i.USBD_Init) refers to usbd_conf.o(i.USBD_LL_Init) for USBD_LL_Init
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_ioreq.o(i.USBD_CtlContinueSendData) for USBD_CtlContinueSendData
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_ioreq.o(i.USBD_CtlReceiveStatus) for USBD_CtlReceiveStatus
    usbd_core.o(i.USBD_LL_DataOutStage) refers to usbd_ioreq.o(i.USBD_CtlContinueRx) for USBD_CtlContinueRx
    usbd_core.o(i.USBD_LL_DataOutStage) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_core.o(i.USBD_LL_DataOutStage) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_core.o(i.USBD_LL_Reset) refers to usbd_conf.o(i.USBD_LL_OpenEP) for USBD_LL_OpenEP
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_ParseSetupRequest) for USBD_ParseSetupRequest
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_StdDevReq) for USBD_StdDevReq
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_StdItfReq) for USBD_StdItfReq
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_StdEPReq) for USBD_StdEPReq
    usbd_core.o(i.USBD_Start) refers to usbd_conf.o(i.USBD_LL_Start) for USBD_LL_Start
    usbd_core.o(i.USBD_Stop) refers to usbd_conf.o(i.USBD_LL_Stop) for USBD_LL_Stop
    usbd_ctlreq.o(i.USBD_CtlError) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(i.USBD_GetDescriptor) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_GetDescriptor) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_GetDescriptor) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_core.o(i.USBD_ClrClassConfig) for USBD_ClrClassConfig
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_core.o(i.USBD_SetClassConfig) for USBD_SetClassConfig
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_ctlreq.o(.data) for .data
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_GetDescriptor) for USBD_GetDescriptor
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_conf.o(i.USBD_LL_SetUSBAddress) for USBD_LL_SetUSBAddress
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_SetConfig) for USBD_SetConfig
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_conf.o(i.USBD_LL_ClearStallEP) for USBD_LL_ClearStallEP
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_conf.o(i.USBD_LL_IsStallEP) for USBD_LL_IsStallEP
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_StdItfReq) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_StdItfReq) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ioreq.o(i.USBD_CtlContinueRx) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(i.USBD_CtlContinueSendData) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(i.USBD_CtlPrepareRx) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(i.USBD_CtlReceiveStatus) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(i.USBD_CtlSendData) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(i.USBD_CtlSendStatus) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(i.USBD_GetRxCount) refers to usbd_conf.o(i.USBD_LL_GetRxDataSize) for USBD_LL_GetRxDataSize
    usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit) refers to usbd_conf.o(i.USBD_LL_CloseEP) for USBD_LL_CloseEP
    usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit) refers to usbd_conf.o(i.USBD_static_free) for USBD_static_free
    usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(i.USBD_CUSTOM_HID_Init) refers to usbd_conf.o(i.USBD_LL_OpenEP) for USBD_LL_OpenEP
    usbd_customhid.o(i.USBD_CUSTOM_HID_Init) refers to usbd_conf.o(i.USBD_static_malloc) for USBD_static_malloc
    usbd_customhid.o(i.USBD_CUSTOM_HID_Init) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_customhid.o(i.USBD_CUSTOM_HID_SendReport) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) refers to usbd_ioreq.o(i.USBD_CtlPrepareRx) for USBD_CtlPrepareRx
    usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_Init) for USBD_CUSTOM_HID_Init
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit) for USBD_CUSTOM_HID_DeInit
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) for USBD_CUSTOM_HID_Setup
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_EP0_RxReady) for USBD_CUSTOM_HID_EP0_RxReady
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_DataIn) for USBD_CUSTOM_HID_DataIn
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut) for USBD_CUSTOM_HID_DataOut
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc) for USBD_CUSTOM_HID_GetHSCfgDesc
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc) for USBD_CUSTOM_HID_GetFSCfgDesc
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) for USBD_CUSTOM_HID_GetOtherSpeedCfgDesc
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc) for USBD_CUSTOM_HID_GetDeviceQualifierDesc
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.bss), (64 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing usb_device.o(.rev16_text), (4 bytes).
    Removing usb_device.o(.revsh_text), (4 bytes).
    Removing usb_device.o(.rrx_text), (6 bytes).
    Removing usbd_desc.o(.rev16_text), (4 bytes).
    Removing usbd_desc.o(.revsh_text), (4 bytes).
    Removing usbd_desc.o(.rrx_text), (6 bytes).
    Removing usbd_custom_hid_if.o(.rev16_text), (4 bytes).
    Removing usbd_custom_hid_if.o(.revsh_text), (4 bytes).
    Removing usbd_custom_hid_if.o(.rrx_text), (6 bytes).
    Removing usbd_conf.o(.rev16_text), (4 bytes).
    Removing usbd_conf.o(.revsh_text), (4 bytes).
    Removing usbd_conf.o(.rrx_text), (6 bytes).
    Removing usbd_conf.o(i.HAL_PCD_ConnectCallback), (8 bytes).
    Removing usbd_conf.o(i.HAL_PCD_DisconnectCallback), (8 bytes).
    Removing usbd_conf.o(i.HAL_PCD_ISOINIncompleteCallback), (8 bytes).
    Removing usbd_conf.o(i.HAL_PCD_ISOOUTIncompleteCallback), (8 bytes).
    Removing usbd_conf.o(i.HAL_PCD_MspDeInit), (36 bytes).
    Removing usbd_conf.o(i.USBD_LL_DeInit), (18 bytes).
    Removing usbd_conf.o(i.USBD_LL_Delay), (4 bytes).
    Removing usbd_conf.o(i.USBD_LL_FlushEP), (18 bytes).
    Removing usbd_conf.o(i.USBD_LL_GetRxDataSize), (8 bytes).
    Removing usbd_conf.o(i.USBD_LL_Stop), (18 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_pcd.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pcd.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pcd.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ConnectCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DataInStageCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DataOutStageCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DeInit), (38 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DevConnect), (44 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DevDisconnect), (44 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DisconnectCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Abort), (30 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Flush), (52 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_GetRxCount), (18 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_GetState), (6 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ISOINIncompleteCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ISOOUTIncompleteCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_MspInit), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ResetCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ResumeCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_SOFCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_SetupStageCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_SuspendCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_WKUP_IRQHandler), (16 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_BCD_Callback), (2 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_LPM_Callback), (2 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_SetConnectionState), (2 bytes).
    Removing stm32f1xx_ll_usb.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_ll_usb.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_ll_usb.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ClearInterrupts), (2 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_EPStopXfer), (86 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_FlushRxFifo), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_FlushTxFifo), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ReadDevAllInEpInterrupt), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ReadDevAllOutEpInterrupt), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ReadDevInEPInterrupt), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ReadDevOutEPInterrupt), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_StopDevice), (18 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq), (32 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq), (32 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort), (70 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT), (152 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (340 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing system_stm32f1xx.o(.constdata), (8 bytes).
    Removing usbd_core.o(.rev16_text), (4 bytes).
    Removing usbd_core.o(.revsh_text), (4 bytes).
    Removing usbd_core.o(.rrx_text), (6 bytes).
    Removing usbd_core.o(i.USBD_DeInit), (38 bytes).
    Removing usbd_core.o(i.USBD_LL_DevConnected), (4 bytes).
    Removing usbd_core.o(i.USBD_LL_DevDisconnected), (22 bytes).
    Removing usbd_core.o(i.USBD_LL_IsoINIncomplete), (4 bytes).
    Removing usbd_core.o(i.USBD_LL_IsoOUTIncomplete), (4 bytes).
    Removing usbd_core.o(i.USBD_RunTestMode), (4 bytes).
    Removing usbd_core.o(i.USBD_Stop), (26 bytes).
    Removing usbd_ctlreq.o(.rev16_text), (4 bytes).
    Removing usbd_ctlreq.o(.revsh_text), (4 bytes).
    Removing usbd_ctlreq.o(.rrx_text), (6 bytes).
    Removing usbd_ioreq.o(.rev16_text), (4 bytes).
    Removing usbd_ioreq.o(.revsh_text), (4 bytes).
    Removing usbd_ioreq.o(.rrx_text), (6 bytes).
    Removing usbd_ioreq.o(i.USBD_GetRxCount), (4 bytes).
    Removing usbd_customhid.o(.rev16_text), (4 bytes).
    Removing usbd_customhid.o(.revsh_text), (4 bytes).
    Removing usbd_customhid.o(.rrx_text), (6 bytes).

255 unused section(s) (total 7748 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pcd.c 0x00000000   Number         0  stm32f1xx_hal_pcd.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pcd_ex.c 0x00000000   Number         0  stm32f1xx_hal_pcd_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_ll_usb.c 0x00000000   Number         0  stm32f1xx_ll_usb.o ABSOLUTE
    ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Src/usbd_customhid.c 0x00000000   Number         0  usbd_customhid.o ABSOLUTE
    ../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_core.c 0x00000000   Number         0  usbd_core.o ABSOLUTE
    ../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ctlreq.c 0x00000000   Number         0  usbd_ctlreq.o ABSOLUTE
    ../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ioreq.c 0x00000000   Number         0  usbd_ioreq.o ABSOLUTE
    ../USB_DEVICE/App/usb_device.c           0x00000000   Number         0  usb_device.o ABSOLUTE
    ../USB_DEVICE/App/usbd_custom_hid_if.c   0x00000000   Number         0  usbd_custom_hid_if.o ABSOLUTE
    ../USB_DEVICE/App/usbd_desc.c            0x00000000   Number         0  usbd_desc.o ABSOLUTE
    ../USB_DEVICE/Target/usbd_conf.c         0x00000000   Number         0  usbd_conf.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pcd.c 0x00000000   Number         0  stm32f1xx_hal_pcd.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pcd_ex.c 0x00000000   Number         0  stm32f1xx_hal_pcd_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_ll_usb.c 0x00000000   Number         0  stm32f1xx_ll_usb.o ABSOLUTE
    ..\Middlewares\ST\STM32_USB_Device_Library\Class\CustomHID\Src\usbd_customhid.c 0x00000000   Number         0  usbd_customhid.o ABSOLUTE
    ..\Middlewares\ST\STM32_USB_Device_Library\Core\Src\usbd_core.c 0x00000000   Number         0  usbd_core.o ABSOLUTE
    ..\Middlewares\ST\STM32_USB_Device_Library\Core\Src\usbd_ctlreq.c 0x00000000   Number         0  usbd_ctlreq.o ABSOLUTE
    ..\Middlewares\ST\STM32_USB_Device_Library\Core\Src\usbd_ioreq.c 0x00000000   Number         0  usbd_ioreq.o ABSOLUTE
    ..\USB_DEVICE\App\usb_device.c           0x00000000   Number         0  usb_device.o ABSOLUTE
    ..\USB_DEVICE\App\usbd_custom_hid_if.c   0x00000000   Number         0  usbd_custom_hid_if.o ABSOLUTE
    ..\USB_DEVICE\App\usbd_desc.c            0x00000000   Number         0  usbd_desc.o ABSOLUTE
    ..\USB_DEVICE\Target\usbd_conf.c         0x00000000   Number         0  usbd_conf.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x08000128   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000184   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a2   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a4   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001a6   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001a8   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001a8   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001a8   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001ae   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001ae   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b2   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001ba   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001bc   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001bc   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c0   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001c8   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x08000208   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000256   Section       86  strncpy.o(.text)
    .text                                    0x080002ac   Section        0  heapauxi.o(.text)
    .text                                    0x080002b2   Section       68  rt_memclr.o(.text)
    .text                                    0x080002f6   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000340   Section        0  exit.o(.text)
    .text                                    0x08000354   Section        8  libspace.o(.text)
    .text                                    0x0800035c   Section        0  sys_exit.o(.text)
    .text                                    0x08000368   Section        2  use_no_semi.o(.text)
    .text                                    0x0800036a   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x0800036a   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.CUSTOM_HID_DeInit_FS                   0x0800036c   Section        0  usbd_custom_hid_if.o(i.CUSTOM_HID_DeInit_FS)
    CUSTOM_HID_DeInit_FS                     0x0800036d   Thumb Code     4  usbd_custom_hid_if.o(i.CUSTOM_HID_DeInit_FS)
    i.CUSTOM_HID_Init_FS                     0x08000370   Section        0  usbd_custom_hid_if.o(i.CUSTOM_HID_Init_FS)
    CUSTOM_HID_Init_FS                       0x08000371   Thumb Code     4  usbd_custom_hid_if.o(i.CUSTOM_HID_Init_FS)
    i.CUSTOM_HID_OutEvent_FS                 0x08000374   Section        0  usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS)
    CUSTOM_HID_OutEvent_FS                   0x08000375   Thumb Code    26  usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS)
    i.DebugMon_Handler                       0x0800039c   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.EXTI0_IRQHandler                       0x0800039e   Section        0  stm32f1xx_it.o(i.EXTI0_IRQHandler)
    i.Error_Handler                          0x080003a4   Section        0  main.o(i.Error_Handler)
    i.HAL_Delay                              0x080003a8   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x080003cc   Section        0  main.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x080003d8   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x080003f0   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x080005d0   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080005dc   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x080005e8   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080005f8   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x0800061c   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x0800065c   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000698   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080006b4   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080006f4   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PCDEx_PMAConfig                    0x08000718   Section        0  stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_PMAConfig)
    i.HAL_PCDEx_SetConnectionState           0x08000754   Section        0  usbd_conf.o(i.HAL_PCDEx_SetConnectionState)
    i.HAL_PCD_ActivateRemoteWakeup           0x08000756   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_ActivateRemoteWakeup)
    i.HAL_PCD_DataInStageCallback            0x0800075c   Section        0  usbd_conf.o(i.HAL_PCD_DataInStageCallback)
    i.HAL_PCD_DataOutStageCallback           0x0800076e   Section        0  usbd_conf.o(i.HAL_PCD_DataOutStageCallback)
    i.HAL_PCD_DeActivateRemoteWakeup         0x08000782   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_DeActivateRemoteWakeup)
    i.HAL_PCD_EP_Close                       0x08000788   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Close)
    i.HAL_PCD_EP_ClrStall                    0x080007d2   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall)
    i.HAL_PCD_EP_DB_Receive                  0x0800082c   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Receive)
    HAL_PCD_EP_DB_Receive                    0x0800082d   Thumb Code   230  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Receive)
    i.HAL_PCD_EP_DB_Transmit                 0x08000912   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit)
    HAL_PCD_EP_DB_Transmit                   0x08000913   Thumb Code   824  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit)
    i.HAL_PCD_EP_Open                        0x08000c4a   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Open)
    i.HAL_PCD_EP_Receive                     0x08000ca2   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Receive)
    i.HAL_PCD_EP_SetStall                    0x08000cd0   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall)
    i.HAL_PCD_EP_Transmit                    0x08000d40   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Transmit)
    i.HAL_PCD_IRQHandler                     0x08000d70   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler)
    i.HAL_PCD_Init                           0x08000ea4   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_Init)
    i.HAL_PCD_MspInit                        0x08000f60   Section        0  usbd_conf.o(i.HAL_PCD_MspInit)
    i.HAL_PCD_ResetCallback                  0x08000f9c   Section        0  usbd_conf.o(i.HAL_PCD_ResetCallback)
    i.HAL_PCD_ResumeCallback                 0x08000fc2   Section        0  usbd_conf.o(i.HAL_PCD_ResumeCallback)
    i.HAL_PCD_SOFCallback                    0x08000fca   Section        0  usbd_conf.o(i.HAL_PCD_SOFCallback)
    i.HAL_PCD_SetAddress                     0x08000fd2   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress)
    i.HAL_PCD_SetupStageCallback             0x08000ff8   Section        0  usbd_conf.o(i.HAL_PCD_SetupStageCallback)
    i.HAL_PCD_Start                          0x08001006   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_Start)
    i.HAL_PCD_Stop                           0x08001038   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop)
    i.HAL_PCD_SuspendCallback                0x0800106c   Section        0  usbd_conf.o(i.HAL_PCD_SuspendCallback)
    i.HAL_RCCEx_PeriphCLKConfig              0x08001090   Section        0  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x0800117c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetSysClockFreq                0x080012a8   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080012f4   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08001614   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HardFault_Handler                      0x0800163c   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.IntToUnicode                           0x08001640   Section        0  usbd_desc.o(i.IntToUnicode)
    IntToUnicode                             0x08001641   Thumb Code    56  usbd_desc.o(i.IntToUnicode)
    i.MX_GPIO_Init                           0x08001678   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USB_DEVICE_Init                     0x08001734   Section        0  usb_device.o(i.MX_USB_DEVICE_Init)
    i.MemManage_Handler                      0x08001788   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800178a   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.PCD_EP_ISR_Handler                     0x0800178c   Section        0  stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler)
    PCD_EP_ISR_Handler                       0x0800178d   Thumb Code   934  stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler)
    i.PendSV_Handler                         0x08001b32   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08001b34   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08001b36   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001b3a   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001bb0   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.USBD_CUSTOM_HID_DataIn                 0x08001bb2   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_DataIn)
    USBD_CUSTOM_HID_DataIn                   0x08001bb3   Thumb Code    14  usbd_customhid.o(i.USBD_CUSTOM_HID_DataIn)
    i.USBD_CUSTOM_HID_DataOut                0x08001bc0   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut)
    USBD_CUSTOM_HID_DataOut                  0x08001bc1   Thumb Code    36  usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut)
    i.USBD_CUSTOM_HID_DeInit                 0x08001be4   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit)
    USBD_CUSTOM_HID_DeInit                   0x08001be5   Thumb Code    56  usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit)
    i.USBD_CUSTOM_HID_EP0_RxReady            0x08001c1c   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_EP0_RxReady)
    USBD_CUSTOM_HID_EP0_RxReady              0x08001c1d   Thumb Code    34  usbd_customhid.o(i.USBD_CUSTOM_HID_EP0_RxReady)
    i.USBD_CUSTOM_HID_GetDeviceQualifierDesc 0x08001c40   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc)
    USBD_CUSTOM_HID_GetDeviceQualifierDesc   0x08001c41   Thumb Code     8  usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc)
    i.USBD_CUSTOM_HID_GetFSCfgDesc           0x08001c4c   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc)
    USBD_CUSTOM_HID_GetFSCfgDesc             0x08001c4d   Thumb Code     8  usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc)
    i.USBD_CUSTOM_HID_GetHSCfgDesc           0x08001c58   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc)
    USBD_CUSTOM_HID_GetHSCfgDesc             0x08001c59   Thumb Code     8  usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc)
    i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc   0x08001c64   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc)
    USBD_CUSTOM_HID_GetOtherSpeedCfgDesc     0x08001c65   Thumb Code     8  usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc)
    i.USBD_CUSTOM_HID_Init                   0x08001c70   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_Init)
    USBD_CUSTOM_HID_Init                     0x08001c71   Thumb Code    84  usbd_customhid.o(i.USBD_CUSTOM_HID_Init)
    i.USBD_CUSTOM_HID_RegisterInterface      0x08001cc4   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_RegisterInterface)
    i.USBD_CUSTOM_HID_SendReport             0x08001cd4   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_SendReport)
    i.USBD_CUSTOM_HID_Setup                  0x08001d00   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_Setup)
    USBD_CUSTOM_HID_Setup                    0x08001d01   Thumb Code   218  usbd_customhid.o(i.USBD_CUSTOM_HID_Setup)
    i.USBD_ClrClassConfig                    0x08001de0   Section        0  usbd_core.o(i.USBD_ClrClassConfig)
    i.USBD_CtlContinueRx                     0x08001dee   Section        0  usbd_ioreq.o(i.USBD_CtlContinueRx)
    i.USBD_CtlContinueSendData               0x08001dfe   Section        0  usbd_ioreq.o(i.USBD_CtlContinueSendData)
    i.USBD_CtlError                          0x08001e0e   Section        0  usbd_ctlreq.o(i.USBD_CtlError)
    i.USBD_CtlPrepareRx                      0x08001e24   Section        0  usbd_ioreq.o(i.USBD_CtlPrepareRx)
    i.USBD_CtlReceiveStatus                  0x08001e42   Section        0  usbd_ioreq.o(i.USBD_CtlReceiveStatus)
    i.USBD_CtlSendData                       0x08001e58   Section        0  usbd_ioreq.o(i.USBD_CtlSendData)
    i.USBD_CtlSendStatus                     0x08001e72   Section        0  usbd_ioreq.o(i.USBD_CtlSendStatus)
    i.USBD_FS_ConfigStrDescriptor            0x08001e88   Section        0  usbd_desc.o(i.USBD_FS_ConfigStrDescriptor)
    i.USBD_FS_DeviceDescriptor               0x08001eb0   Section        0  usbd_desc.o(i.USBD_FS_DeviceDescriptor)
    i.USBD_FS_InterfaceStrDescriptor         0x08001ebc   Section        0  usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor)
    i.USBD_FS_LangIDStrDescriptor            0x08001ee8   Section        0  usbd_desc.o(i.USBD_FS_LangIDStrDescriptor)
    i.USBD_FS_ManufacturerStrDescriptor      0x08001ef4   Section        0  usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor)
    i.USBD_FS_ProductStrDescriptor           0x08001f14   Section        0  usbd_desc.o(i.USBD_FS_ProductStrDescriptor)
    i.USBD_FS_SerialStrDescriptor            0x08001f48   Section        0  usbd_desc.o(i.USBD_FS_SerialStrDescriptor)
    i.USBD_GetDescriptor                     0x08001f80   Section        0  usbd_ctlreq.o(i.USBD_GetDescriptor)
    USBD_GetDescriptor                       0x08001f81   Thumb Code   232  usbd_ctlreq.o(i.USBD_GetDescriptor)
    i.USBD_GetString                         0x08002068   Section        0  usbd_ctlreq.o(i.USBD_GetString)
    i.USBD_Get_USB_Status                    0x080020b0   Section        0  usbd_conf.o(i.USBD_Get_USB_Status)
    USBD_Get_USB_Status                      0x080020b1   Thumb Code    24  usbd_conf.o(i.USBD_Get_USB_Status)
    i.USBD_Init                              0x080020c8   Section        0  usbd_core.o(i.USBD_Init)
    i.USBD_LL_ClearStallEP                   0x080020f2   Section        0  usbd_conf.o(i.USBD_LL_ClearStallEP)
    i.USBD_LL_CloseEP                        0x08002104   Section        0  usbd_conf.o(i.USBD_LL_CloseEP)
    i.USBD_LL_DataInStage                    0x08002116   Section        0  usbd_core.o(i.USBD_LL_DataInStage)
    i.USBD_LL_DataOutStage                   0x080021dc   Section        0  usbd_core.o(i.USBD_LL_DataOutStage)
    i.USBD_LL_Init                           0x08002260   Section        0  usbd_conf.o(i.USBD_LL_Init)
    i.USBD_LL_IsStallEP                      0x080022d0   Section        0  usbd_conf.o(i.USBD_LL_IsStallEP)
    i.USBD_LL_OpenEP                         0x080022ee   Section        0  usbd_conf.o(i.USBD_LL_OpenEP)
    i.USBD_LL_PrepareReceive                 0x08002306   Section        0  usbd_conf.o(i.USBD_LL_PrepareReceive)
    i.USBD_LL_Reset                          0x08002318   Section        0  usbd_core.o(i.USBD_LL_Reset)
    i.USBD_LL_Resume                         0x08002368   Section        0  usbd_core.o(i.USBD_LL_Resume)
    i.USBD_LL_SOF                            0x0800237c   Section        0  usbd_core.o(i.USBD_LL_SOF)
    i.USBD_LL_SetSpeed                       0x08002394   Section        0  usbd_core.o(i.USBD_LL_SetSpeed)
    i.USBD_LL_SetUSBAddress                  0x0800239a   Section        0  usbd_conf.o(i.USBD_LL_SetUSBAddress)
    i.USBD_LL_SetupStage                     0x080023ac   Section        0  usbd_core.o(i.USBD_LL_SetupStage)
    i.USBD_LL_StallEP                        0x08002406   Section        0  usbd_conf.o(i.USBD_LL_StallEP)
    i.USBD_LL_Start                          0x08002418   Section        0  usbd_conf.o(i.USBD_LL_Start)
    i.USBD_LL_Suspend                        0x0800242a   Section        0  usbd_core.o(i.USBD_LL_Suspend)
    i.USBD_LL_Transmit                       0x0800243c   Section        0  usbd_conf.o(i.USBD_LL_Transmit)
    i.USBD_ParseSetupRequest                 0x0800244e   Section        0  usbd_ctlreq.o(i.USBD_ParseSetupRequest)
    i.USBD_RegisterClass                     0x08002476   Section        0  usbd_core.o(i.USBD_RegisterClass)
    i.USBD_SetClassConfig                    0x08002486   Section        0  usbd_core.o(i.USBD_SetClassConfig)
    i.USBD_SetConfig                         0x0800249c   Section        0  usbd_ctlreq.o(i.USBD_SetConfig)
    USBD_SetConfig                           0x0800249d   Thumb Code   128  usbd_ctlreq.o(i.USBD_SetConfig)
    i.USBD_Start                             0x08002520   Section        0  usbd_core.o(i.USBD_Start)
    i.USBD_StdDevReq                         0x0800252a   Section        0  usbd_ctlreq.o(i.USBD_StdDevReq)
    i.USBD_StdEPReq                          0x0800263c   Section        0  usbd_ctlreq.o(i.USBD_StdEPReq)
    i.USBD_StdItfReq                         0x0800278c   Section        0  usbd_ctlreq.o(i.USBD_StdItfReq)
    i.USBD_static_free                       0x080027da   Section        0  usbd_conf.o(i.USBD_static_free)
    i.USBD_static_malloc                     0x080027dc   Section        0  usbd_conf.o(i.USBD_static_malloc)
    i.USB_ActivateEndpoint                   0x080027e4   Section        0  stm32f1xx_ll_usb.o(i.USB_ActivateEndpoint)
    i.USB_ActivateRemoteWakeup               0x08002a80   Section        0  stm32f1xx_ll_usb.o(i.USB_ActivateRemoteWakeup)
    i.USB_CoreInit                           0x08002a8e   Section        0  stm32f1xx_ll_usb.o(i.USB_CoreInit)
    i.USB_DeActivateRemoteWakeup             0x08002a92   Section        0  stm32f1xx_ll_usb.o(i.USB_DeActivateRemoteWakeup)
    i.USB_DeactivateEndpoint                 0x08002aa0   Section        0  stm32f1xx_ll_usb.o(i.USB_DeactivateEndpoint)
    i.USB_DevConnect                         0x08002bae   Section        0  stm32f1xx_ll_usb.o(i.USB_DevConnect)
    i.USB_DevDisconnect                      0x08002bb2   Section        0  stm32f1xx_ll_usb.o(i.USB_DevDisconnect)
    i.USB_DevInit                            0x08002bb6   Section        0  stm32f1xx_ll_usb.o(i.USB_DevInit)
    i.USB_DisableGlobalInt                   0x08002bc8   Section        0  stm32f1xx_ll_usb.o(i.USB_DisableGlobalInt)
    i.USB_EP0_OutStart                       0x08002bd6   Section        0  stm32f1xx_ll_usb.o(i.USB_EP0_OutStart)
    i.USB_EPClearStall                       0x08002bda   Section        0  stm32f1xx_ll_usb.o(i.USB_EPClearStall)
    i.USB_EPSetStall                         0x08002c52   Section        0  stm32f1xx_ll_usb.o(i.USB_EPSetStall)
    i.USB_EPStartXfer                        0x08002c84   Section        0  stm32f1xx_ll_usb.o(i.USB_EPStartXfer)
    i.USB_EnableGlobalInt                    0x08003166   Section        0  stm32f1xx_ll_usb.o(i.USB_EnableGlobalInt)
    i.USB_IO_rest                            0x08003178   Section        0  main.o(i.USB_IO_rest)
    i.USB_LP_CAN1_RX0_IRQHandler             0x080031d0   Section        0  stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler)
    i.USB_MonitorStatus                      0x080031dc   Section        0  main.o(i.USB_MonitorStatus)
    i.USB_ReadInterrupts                     0x08003268   Section        0  stm32f1xx_ll_usb.o(i.USB_ReadInterrupts)
    i.USB_ReadPMA                            0x08003270   Section        0  stm32f1xx_ll_usb.o(i.USB_ReadPMA)
    i.USB_SetCurrentMode                     0x0800329c   Section        0  stm32f1xx_ll_usb.o(i.USB_SetCurrentMode)
    i.USB_SetDevAddress                      0x080032a0   Section        0  stm32f1xx_ll_usb.o(i.USB_SetDevAddress)
    i.USB_WakeupDevice                       0x080032ac   Section        0  main.o(i.USB_WakeupDevice)
    i.USB_WritePMA                           0x080032e8   Section        0  stm32f1xx_ll_usb.o(i.USB_WritePMA)
    i.UsageFault_Handler                     0x0800330e   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x08003310   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08003311   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.main                                   0x08003330   Section        0  main.o(i.main)
    .constdata                               0x080033f8   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x080033f8   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x080033fa   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x0800340a   Section       16  system_stm32f1xx.o(.constdata)
    .data                                    0x20000000   Section       72  main.o(.data)
    usb_was_suspended                        0x20000001   Data           1  main.o(.data)
    prev_state                               0x20000002   Data           1  main.o(.data)
    last_activity_time                       0x20000004   Data           4  main.o(.data)
    .data                                    0x20000048   Section        1  main.o(.data)
    .data                                    0x2000004c   Section       28  usbd_desc.o(.data)
    .data                                    0x20000068   Section       50  usbd_desc.o(.data)
    .data                                    0x2000009c   Section       25  usbd_custom_hid_if.o(.data)
    CUSTOM_HID_ReportDesc_FS                 0x2000009c   Data          25  usbd_custom_hid_if.o(.data)
    .data                                    0x200000b8   Section       16  usbd_custom_hid_if.o(.data)
    .data                                    0x200000c8   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x200000d4   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x200000d8   Section        1  usbd_ctlreq.o(.data)
    cfgidx                                   0x200000d8   Data           1  usbd_ctlreq.o(.data)
    .data                                    0x200000dc   Section       56  usbd_customhid.o(.data)
    .data                                    0x20000114   Section      154  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_CfgFSDesc                0x20000114   Data          41  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_CfgHSDesc                0x20000140   Data          41  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_OtherSpeedCfgDesc        0x2000016c   Data          41  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_Desc                     0x20000198   Data           9  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_DeviceQualifierDesc      0x200001a4   Data          10  usbd_customhid.o(.data)
    .bss                                     0x200001b0   Section      708  usb_device.o(.bss)
    .bss                                     0x20000474   Section      512  usbd_desc.o(.bss)
    .bss                                     0x20000674   Section      816  usbd_conf.o(.bss)
    mem                                      0x20000674   Data          88  usbd_conf.o(.bss)
    .bss                                     0x200009a4   Section       96  libspace.o(.bss)
    HEAP                                     0x20000a08   Section      512  startup_stm32f103xb.o(HEAP)
    Heap_Mem                                 0x20000a08   Data         512  startup_stm32f103xb.o(HEAP)
    STACK                                    0x20000c08   Section     1024  startup_stm32f103xb.o(STACK)
    Stack_Mem                                0x20000c08   Data        1024  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x20001008   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x08000129   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x08000129   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000185   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a5   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001a9   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001a9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001a9   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001af   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001af   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bb   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001bd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001bd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001c9   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART1_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART2_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x080001e5   Thumb Code     0  startup_stm32f103xb.o(.text)
    __aeabi_memclr4                          0x08000209   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000209   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000209   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800020d   Thumb Code     0  rt_memclr_w.o(.text)
    strncpy                                  0x08000257   Thumb Code    86  strncpy.o(.text)
    __use_two_region_memory                  0x080002ad   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002af   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002b1   Thumb Code     2  heapauxi.o(.text)
    __aeabi_memclr                           0x080002b3   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080002b3   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080002b7   Thumb Code     0  rt_memclr.o(.text)
    __user_setup_stackheap                   0x080002f7   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000341   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000355   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000355   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000355   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x0800035d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000369   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000369   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x0800036b   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x0800036b   Thumb Code     0  indicate_semi.o(.text)
    DebugMon_Handler                         0x0800039d   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    EXTI0_IRQHandler                         0x0800039f   Thumb Code     6  stm32f1xx_it.o(i.EXTI0_IRQHandler)
    Error_Handler                            0x080003a5   Thumb Code     4  main.o(i.Error_Handler)
    HAL_Delay                                0x080003a9   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x080003cd   Thumb Code     8  main.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x080003d9   Thumb Code    18  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x080003f1   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x080005d1   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080005dd   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x080005e9   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080005f9   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x0800061d   Thumb Code    54  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x0800065d   Thumb Code    52  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000699   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080006b5   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080006f5   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PCDEx_PMAConfig                      0x08000719   Thumb Code    60  stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_PMAConfig)
    HAL_PCDEx_SetConnectionState             0x08000755   Thumb Code     2  usbd_conf.o(i.HAL_PCDEx_SetConnectionState)
    HAL_PCD_ActivateRemoteWakeup             0x08000757   Thumb Code     6  stm32f1xx_hal_pcd.o(i.HAL_PCD_ActivateRemoteWakeup)
    HAL_PCD_DataInStageCallback              0x0800075d   Thumb Code    18  usbd_conf.o(i.HAL_PCD_DataInStageCallback)
    HAL_PCD_DataOutStageCallback             0x0800076f   Thumb Code    20  usbd_conf.o(i.HAL_PCD_DataOutStageCallback)
    HAL_PCD_DeActivateRemoteWakeup           0x08000783   Thumb Code     6  stm32f1xx_hal_pcd.o(i.HAL_PCD_DeActivateRemoteWakeup)
    HAL_PCD_EP_Close                         0x08000789   Thumb Code    74  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Close)
    HAL_PCD_EP_ClrStall                      0x080007d3   Thumb Code    90  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall)
    HAL_PCD_EP_Open                          0x08000c4b   Thumb Code    88  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Open)
    HAL_PCD_EP_Receive                       0x08000ca3   Thumb Code    46  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Receive)
    HAL_PCD_EP_SetStall                      0x08000cd1   Thumb Code   112  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall)
    HAL_PCD_EP_Transmit                      0x08000d41   Thumb Code    46  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Transmit)
    HAL_PCD_IRQHandler                       0x08000d71   Thumb Code   308  stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler)
    HAL_PCD_Init                             0x08000ea5   Thumb Code   186  stm32f1xx_hal_pcd.o(i.HAL_PCD_Init)
    HAL_PCD_MspInit                          0x08000f61   Thumb Code    50  usbd_conf.o(i.HAL_PCD_MspInit)
    HAL_PCD_ResetCallback                    0x08000f9d   Thumb Code    38  usbd_conf.o(i.HAL_PCD_ResetCallback)
    HAL_PCD_ResumeCallback                   0x08000fc3   Thumb Code     8  usbd_conf.o(i.HAL_PCD_ResumeCallback)
    HAL_PCD_SOFCallback                      0x08000fcb   Thumb Code     8  usbd_conf.o(i.HAL_PCD_SOFCallback)
    HAL_PCD_SetAddress                       0x08000fd3   Thumb Code    38  stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress)
    HAL_PCD_SetupStageCallback               0x08000ff9   Thumb Code    14  usbd_conf.o(i.HAL_PCD_SetupStageCallback)
    HAL_PCD_Start                            0x08001007   Thumb Code    50  stm32f1xx_hal_pcd.o(i.HAL_PCD_Start)
    HAL_PCD_Stop                             0x08001039   Thumb Code    50  stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop)
    HAL_PCD_SuspendCallback                  0x0800106d   Thumb Code    30  usbd_conf.o(i.HAL_PCD_SuspendCallback)
    HAL_RCCEx_PeriphCLKConfig                0x08001091   Thumb Code   224  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x0800117d   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetSysClockFreq                  0x080012a9   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080012f5   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001615   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HardFault_Handler                        0x0800163d   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    MX_GPIO_Init                             0x08001679   Thumb Code   170  gpio.o(i.MX_GPIO_Init)
    MX_USB_DEVICE_Init                       0x08001735   Thumb Code    66  usb_device.o(i.MX_USB_DEVICE_Init)
    MemManage_Handler                        0x08001789   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800178b   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08001b33   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08001b35   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001b37   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001b3b   Thumb Code   118  main.o(i.SystemClock_Config)
    SystemInit                               0x08001bb1   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    USBD_CUSTOM_HID_RegisterInterface        0x08001cc5   Thumb Code    16  usbd_customhid.o(i.USBD_CUSTOM_HID_RegisterInterface)
    USBD_CUSTOM_HID_SendReport               0x08001cd5   Thumb Code    44  usbd_customhid.o(i.USBD_CUSTOM_HID_SendReport)
    USBD_ClrClassConfig                      0x08001de1   Thumb Code    14  usbd_core.o(i.USBD_ClrClassConfig)
    USBD_CtlContinueRx                       0x08001def   Thumb Code    16  usbd_ioreq.o(i.USBD_CtlContinueRx)
    USBD_CtlContinueSendData                 0x08001dff   Thumb Code    16  usbd_ioreq.o(i.USBD_CtlContinueSendData)
    USBD_CtlError                            0x08001e0f   Thumb Code    22  usbd_ctlreq.o(i.USBD_CtlError)
    USBD_CtlPrepareRx                        0x08001e25   Thumb Code    30  usbd_ioreq.o(i.USBD_CtlPrepareRx)
    USBD_CtlReceiveStatus                    0x08001e43   Thumb Code    22  usbd_ioreq.o(i.USBD_CtlReceiveStatus)
    USBD_CtlSendData                         0x08001e59   Thumb Code    26  usbd_ioreq.o(i.USBD_CtlSendData)
    USBD_CtlSendStatus                       0x08001e73   Thumb Code    22  usbd_ioreq.o(i.USBD_CtlSendStatus)
    USBD_FS_ConfigStrDescriptor              0x08001e89   Thumb Code    16  usbd_desc.o(i.USBD_FS_ConfigStrDescriptor)
    USBD_FS_DeviceDescriptor                 0x08001eb1   Thumb Code     8  usbd_desc.o(i.USBD_FS_DeviceDescriptor)
    USBD_FS_InterfaceStrDescriptor           0x08001ebd   Thumb Code    16  usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor)
    USBD_FS_LangIDStrDescriptor              0x08001ee9   Thumb Code     8  usbd_desc.o(i.USBD_FS_LangIDStrDescriptor)
    USBD_FS_ManufacturerStrDescriptor        0x08001ef5   Thumb Code    16  usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor)
    USBD_FS_ProductStrDescriptor             0x08001f15   Thumb Code    16  usbd_desc.o(i.USBD_FS_ProductStrDescriptor)
    USBD_FS_SerialStrDescriptor              0x08001f49   Thumb Code    48  usbd_desc.o(i.USBD_FS_SerialStrDescriptor)
    USBD_GetString                           0x08002069   Thumb Code    72  usbd_ctlreq.o(i.USBD_GetString)
    USBD_Init                                0x080020c9   Thumb Code    42  usbd_core.o(i.USBD_Init)
    USBD_LL_ClearStallEP                     0x080020f3   Thumb Code    18  usbd_conf.o(i.USBD_LL_ClearStallEP)
    USBD_LL_CloseEP                          0x08002105   Thumb Code    18  usbd_conf.o(i.USBD_LL_CloseEP)
    USBD_LL_DataInStage                      0x08002117   Thumb Code   198  usbd_core.o(i.USBD_LL_DataInStage)
    USBD_LL_DataOutStage                     0x080021dd   Thumb Code   130  usbd_core.o(i.USBD_LL_DataOutStage)
    USBD_LL_Init                             0x08002261   Thumb Code   104  usbd_conf.o(i.USBD_LL_Init)
    USBD_LL_IsStallEP                        0x080022d1   Thumb Code    30  usbd_conf.o(i.USBD_LL_IsStallEP)
    USBD_LL_OpenEP                           0x080022ef   Thumb Code    24  usbd_conf.o(i.USBD_LL_OpenEP)
    USBD_LL_PrepareReceive                   0x08002307   Thumb Code    18  usbd_conf.o(i.USBD_LL_PrepareReceive)
    USBD_LL_Reset                            0x08002319   Thumb Code    80  usbd_core.o(i.USBD_LL_Reset)
    USBD_LL_Resume                           0x08002369   Thumb Code    20  usbd_core.o(i.USBD_LL_Resume)
    USBD_LL_SOF                              0x0800237d   Thumb Code    24  usbd_core.o(i.USBD_LL_SOF)
    USBD_LL_SetSpeed                         0x08002395   Thumb Code     6  usbd_core.o(i.USBD_LL_SetSpeed)
    USBD_LL_SetUSBAddress                    0x0800239b   Thumb Code    18  usbd_conf.o(i.USBD_LL_SetUSBAddress)
    USBD_LL_SetupStage                       0x080023ad   Thumb Code    90  usbd_core.o(i.USBD_LL_SetupStage)
    USBD_LL_StallEP                          0x08002407   Thumb Code    18  usbd_conf.o(i.USBD_LL_StallEP)
    USBD_LL_Start                            0x08002419   Thumb Code    18  usbd_conf.o(i.USBD_LL_Start)
    USBD_LL_Suspend                          0x0800242b   Thumb Code    18  usbd_core.o(i.USBD_LL_Suspend)
    USBD_LL_Transmit                         0x0800243d   Thumb Code    18  usbd_conf.o(i.USBD_LL_Transmit)
    USBD_ParseSetupRequest                   0x0800244f   Thumb Code    40  usbd_ctlreq.o(i.USBD_ParseSetupRequest)
    USBD_RegisterClass                       0x08002477   Thumb Code    16  usbd_core.o(i.USBD_RegisterClass)
    USBD_SetClassConfig                      0x08002487   Thumb Code    22  usbd_core.o(i.USBD_SetClassConfig)
    USBD_Start                               0x08002521   Thumb Code    10  usbd_core.o(i.USBD_Start)
    USBD_StdDevReq                           0x0800252b   Thumb Code   274  usbd_ctlreq.o(i.USBD_StdDevReq)
    USBD_StdEPReq                            0x0800263d   Thumb Code   336  usbd_ctlreq.o(i.USBD_StdEPReq)
    USBD_StdItfReq                           0x0800278d   Thumb Code    78  usbd_ctlreq.o(i.USBD_StdItfReq)
    USBD_static_free                         0x080027db   Thumb Code     2  usbd_conf.o(i.USBD_static_free)
    USBD_static_malloc                       0x080027dd   Thumb Code     4  usbd_conf.o(i.USBD_static_malloc)
    USB_ActivateEndpoint                     0x080027e5   Thumb Code   668  stm32f1xx_ll_usb.o(i.USB_ActivateEndpoint)
    USB_ActivateRemoteWakeup                 0x08002a81   Thumb Code    14  stm32f1xx_ll_usb.o(i.USB_ActivateRemoteWakeup)
    USB_CoreInit                             0x08002a8f   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_CoreInit)
    USB_DeActivateRemoteWakeup               0x08002a93   Thumb Code    14  stm32f1xx_ll_usb.o(i.USB_DeActivateRemoteWakeup)
    USB_DeactivateEndpoint                   0x08002aa1   Thumb Code   270  stm32f1xx_ll_usb.o(i.USB_DeactivateEndpoint)
    USB_DevConnect                           0x08002baf   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_DevConnect)
    USB_DevDisconnect                        0x08002bb3   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_DevDisconnect)
    USB_DevInit                              0x08002bb7   Thumb Code    18  stm32f1xx_ll_usb.o(i.USB_DevInit)
    USB_DisableGlobalInt                     0x08002bc9   Thumb Code    14  stm32f1xx_ll_usb.o(i.USB_DisableGlobalInt)
    USB_EP0_OutStart                         0x08002bd7   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_EP0_OutStart)
    USB_EPClearStall                         0x08002bdb   Thumb Code   120  stm32f1xx_ll_usb.o(i.USB_EPClearStall)
    USB_EPSetStall                           0x08002c53   Thumb Code    50  stm32f1xx_ll_usb.o(i.USB_EPSetStall)
    USB_EPStartXfer                          0x08002c85   Thumb Code  1250  stm32f1xx_ll_usb.o(i.USB_EPStartXfer)
    USB_EnableGlobalInt                      0x08003167   Thumb Code    18  stm32f1xx_ll_usb.o(i.USB_EnableGlobalInt)
    USB_IO_rest                              0x08003179   Thumb Code    78  main.o(i.USB_IO_rest)
    USB_LP_CAN1_RX0_IRQHandler               0x080031d1   Thumb Code     6  stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler)
    USB_MonitorStatus                        0x080031dd   Thumb Code   122  main.o(i.USB_MonitorStatus)
    USB_ReadInterrupts                       0x08003269   Thumb Code     6  stm32f1xx_ll_usb.o(i.USB_ReadInterrupts)
    USB_ReadPMA                              0x08003271   Thumb Code    44  stm32f1xx_ll_usb.o(i.USB_ReadPMA)
    USB_SetCurrentMode                       0x0800329d   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_SetCurrentMode)
    USB_SetDevAddress                        0x080032a1   Thumb Code    12  stm32f1xx_ll_usb.o(i.USB_SetDevAddress)
    USB_WakeupDevice                         0x080032ad   Thumb Code    50  main.o(i.USB_WakeupDevice)
    USB_WritePMA                             0x080032e9   Thumb Code    38  stm32f1xx_ll_usb.o(i.USB_WritePMA)
    UsageFault_Handler                       0x0800330f   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    main                                     0x08003331   Thumb Code   186  main.o(i.main)
    AHBPrescTable                            0x0800340a   Data          16  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x0800341c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800343c   Number         0  anon$$obj.o(Region$$Table)
    key_flag                                 0x20000000   Data           1  main.o(.data)
    usb_rx_buffer                            0x20000008   Data          64  main.o(.data)
    usb_rx_flag                              0x20000048   Data           1  main.o(.data)
    FS_Desc                                  0x2000004c   Data          28  usbd_desc.o(.data)
    USBD_LangIDDesc                          0x20000068   Data           4  usbd_desc.o(.data)
    USBD_FS_DeviceDesc                       0x2000006c   Data          18  usbd_desc.o(.data)
    USBD_StringSerial                        0x20000080   Data          26  usbd_desc.o(.data)
    USBD_CustomHID_fops_FS                   0x200000b8   Data          16  usbd_custom_hid_if.o(.data)
    uwTickFreq                               0x200000c8   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x200000cc   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x200000d0   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x200000d4   Data           4  system_stm32f1xx.o(.data)
    USBD_CUSTOM_HID                          0x200000dc   Data          56  usbd_customhid.o(.data)
    hUsbDeviceFS                             0x200001b0   Data         708  usb_device.o(.bss)
    USBD_StrDesc                             0x20000474   Data         512  usbd_desc.o(.bss)
    hpcd_USB_FS                              0x200006cc   Data         728  usbd_conf.o(.bss)
    __libspace_start                         0x200009a4   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000a04   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000035ec, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x00003500])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000343c, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         2584  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         2747    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000005a   Code   RO         2745    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000182   0x08000182   0x00000002   PAD
    0x08000184   0x08000184   0x0000001c   Code   RO         2749    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a0   0x080001a0   0x00000002   Code   RO         2613    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2620    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2622    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2625    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2627    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2629    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2632    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2634    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2636    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2638    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2640    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2642    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2644    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2646    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2648    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2650    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2652    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2656    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2658    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2660    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2662    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000002   Code   RO         2663    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a4   0x080001a4   0x00000002   Code   RO         2683    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2696    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2698    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2700    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2703    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2706    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2708    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2711    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000002   Code   RO         2712    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001a8   0x080001a8   0x00000000   Code   RO         2586    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001a8   0x080001a8   0x00000000   Code   RO         2590    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001a8   0x080001a8   0x00000006   Code   RO         2602    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001ae   0x080001ae   0x00000000   Code   RO         2592    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001ae   0x080001ae   0x00000004   Code   RO         2593    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b2   0x080001b2   0x00000000   Code   RO         2595    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b2   0x080001b2   0x00000008   Code   RO         2596    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001ba   0x080001ba   0x00000002   Code   RO         2617    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         2665    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         2666    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c0   0x080001c0   0x00000006   Code   RO         2667    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001c6   0x080001c6   0x00000002   PAD
    0x080001c8   0x080001c8   0x00000040   Code   RO            4    .text               startup_stm32f103xb.o
    0x08000208   0x08000208   0x0000004e   Code   RO         2578    .text               c_w.l(rt_memclr_w.o)
    0x08000256   0x08000256   0x00000056   Code   RO         2580    .text               c_w.l(strncpy.o)
    0x080002ac   0x080002ac   0x00000006   Code   RO         2582    .text               c_w.l(heapauxi.o)
    0x080002b2   0x080002b2   0x00000044   Code   RO         2587    .text               c_w.l(rt_memclr.o)
    0x080002f6   0x080002f6   0x0000004a   Code   RO         2604    .text               c_w.l(sys_stackheap_outer.o)
    0x08000340   0x08000340   0x00000012   Code   RO         2606    .text               c_w.l(exit.o)
    0x08000352   0x08000352   0x00000002   PAD
    0x08000354   0x08000354   0x00000008   Code   RO         2614    .text               c_w.l(libspace.o)
    0x0800035c   0x0800035c   0x0000000c   Code   RO         2675    .text               c_w.l(sys_exit.o)
    0x08000368   0x08000368   0x00000002   Code   RO         2686    .text               c_w.l(use_no_semi.o)
    0x0800036a   0x0800036a   0x00000000   Code   RO         2688    .text               c_w.l(indicate_semi.o)
    0x0800036a   0x0800036a   0x00000002   Code   RO          225    i.BusFault_Handler  stm32f1xx_it.o
    0x0800036c   0x0800036c   0x00000004   Code   RO          458    i.CUSTOM_HID_DeInit_FS  usbd_custom_hid_if.o
    0x08000370   0x08000370   0x00000004   Code   RO          459    i.CUSTOM_HID_Init_FS  usbd_custom_hid_if.o
    0x08000374   0x08000374   0x00000028   Code   RO          460    i.CUSTOM_HID_OutEvent_FS  usbd_custom_hid_if.o
    0x0800039c   0x0800039c   0x00000002   Code   RO          226    i.DebugMon_Handler  stm32f1xx_it.o
    0x0800039e   0x0800039e   0x00000006   Code   RO          227    i.EXTI0_IRQHandler  stm32f1xx_it.o
    0x080003a4   0x080003a4   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x080003a8   0x080003a8   0x00000024   Code   RO         1219    i.HAL_Delay         stm32f1xx_hal.o
    0x080003cc   0x080003cc   0x0000000c   Code   RO           14    i.HAL_GPIO_EXTI_Callback  main.o
    0x080003d8   0x080003d8   0x00000018   Code   RO         1528    i.HAL_GPIO_EXTI_IRQHandler  stm32f1xx_hal_gpio.o
    0x080003f0   0x080003f0   0x000001e0   Code   RO         1529    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x080005d0   0x080005d0   0x0000000a   Code   RO         1533    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x080005da   0x080005da   0x00000002   PAD
    0x080005dc   0x080005dc   0x0000000c   Code   RO         1223    i.HAL_GetTick       stm32f1xx_hal.o
    0x080005e8   0x080005e8   0x00000010   Code   RO         1229    i.HAL_IncTick       stm32f1xx_hal.o
    0x080005f8   0x080005f8   0x00000024   Code   RO         1230    i.HAL_Init          stm32f1xx_hal.o
    0x0800061c   0x0800061c   0x00000040   Code   RO         1231    i.HAL_InitTick      stm32f1xx_hal.o
    0x0800065c   0x0800065c   0x0000003c   Code   RO          313    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08000698   0x08000698   0x0000001a   Code   RO         1689    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x080006b2   0x080006b2   0x00000002   PAD
    0x080006b4   0x080006b4   0x00000040   Code   RO         1695    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080006f4   0x080006f4   0x00000024   Code   RO         1696    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08000718   0x08000718   0x0000003c   Code   RO          986    i.HAL_PCDEx_PMAConfig  stm32f1xx_hal_pcd_ex.o
    0x08000754   0x08000754   0x00000002   Code   RO          499    i.HAL_PCDEx_SetConnectionState  usbd_conf.o
    0x08000756   0x08000756   0x00000006   Code   RO          747    i.HAL_PCD_ActivateRemoteWakeup  stm32f1xx_hal_pcd.o
    0x0800075c   0x0800075c   0x00000012   Code   RO          501    i.HAL_PCD_DataInStageCallback  usbd_conf.o
    0x0800076e   0x0800076e   0x00000014   Code   RO          502    i.HAL_PCD_DataOutStageCallback  usbd_conf.o
    0x08000782   0x08000782   0x00000006   Code   RO          751    i.HAL_PCD_DeActivateRemoteWakeup  stm32f1xx_hal_pcd.o
    0x08000788   0x08000788   0x0000004a   Code   RO          757    i.HAL_PCD_EP_Close  stm32f1xx_hal_pcd.o
    0x080007d2   0x080007d2   0x0000005a   Code   RO          758    i.HAL_PCD_EP_ClrStall  stm32f1xx_hal_pcd.o
    0x0800082c   0x0800082c   0x000000e6   Code   RO          759    i.HAL_PCD_EP_DB_Receive  stm32f1xx_hal_pcd.o
    0x08000912   0x08000912   0x00000338   Code   RO          760    i.HAL_PCD_EP_DB_Transmit  stm32f1xx_hal_pcd.o
    0x08000c4a   0x08000c4a   0x00000058   Code   RO          763    i.HAL_PCD_EP_Open   stm32f1xx_hal_pcd.o
    0x08000ca2   0x08000ca2   0x0000002e   Code   RO          764    i.HAL_PCD_EP_Receive  stm32f1xx_hal_pcd.o
    0x08000cd0   0x08000cd0   0x00000070   Code   RO          765    i.HAL_PCD_EP_SetStall  stm32f1xx_hal_pcd.o
    0x08000d40   0x08000d40   0x0000002e   Code   RO          766    i.HAL_PCD_EP_Transmit  stm32f1xx_hal_pcd.o
    0x08000d6e   0x08000d6e   0x00000002   PAD
    0x08000d70   0x08000d70   0x00000134   Code   RO          768    i.HAL_PCD_IRQHandler  stm32f1xx_hal_pcd.o
    0x08000ea4   0x08000ea4   0x000000ba   Code   RO          771    i.HAL_PCD_Init      stm32f1xx_hal_pcd.o
    0x08000f5e   0x08000f5e   0x00000002   PAD
    0x08000f60   0x08000f60   0x0000003c   Code   RO          507    i.HAL_PCD_MspInit   usbd_conf.o
    0x08000f9c   0x08000f9c   0x00000026   Code   RO          508    i.HAL_PCD_ResetCallback  usbd_conf.o
    0x08000fc2   0x08000fc2   0x00000008   Code   RO          509    i.HAL_PCD_ResumeCallback  usbd_conf.o
    0x08000fca   0x08000fca   0x00000008   Code   RO          510    i.HAL_PCD_SOFCallback  usbd_conf.o
    0x08000fd2   0x08000fd2   0x00000026   Code   RO          777    i.HAL_PCD_SetAddress  stm32f1xx_hal_pcd.o
    0x08000ff8   0x08000ff8   0x0000000e   Code   RO          511    i.HAL_PCD_SetupStageCallback  usbd_conf.o
    0x08001006   0x08001006   0x00000032   Code   RO          779    i.HAL_PCD_Start     stm32f1xx_hal_pcd.o
    0x08001038   0x08001038   0x00000032   Code   RO          780    i.HAL_PCD_Stop      stm32f1xx_hal_pcd.o
    0x0800106a   0x0800106a   0x00000002   PAD
    0x0800106c   0x0800106c   0x00000024   Code   RO          512    i.HAL_PCD_SuspendCallback  usbd_conf.o
    0x08001090   0x08001090   0x000000ec   Code   RO         1491    i.HAL_RCCEx_PeriphCLKConfig  stm32f1xx_hal_rcc_ex.o
    0x0800117c   0x0800117c   0x0000012c   Code   RO         1387    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x080012a8   0x080012a8   0x0000004c   Code   RO         1396    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x080012f4   0x080012f4   0x00000320   Code   RO         1399    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08001614   0x08001614   0x00000028   Code   RO         1700    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x0800163c   0x0800163c   0x00000002   Code   RO          228    i.HardFault_Handler  stm32f1xx_it.o
    0x0800163e   0x0800163e   0x00000002   PAD
    0x08001640   0x08001640   0x00000038   Code   RO          372    i.IntToUnicode      usbd_desc.o
    0x08001678   0x08001678   0x000000bc   Code   RO          201    i.MX_GPIO_Init      gpio.o
    0x08001734   0x08001734   0x00000054   Code   RO          337    i.MX_USB_DEVICE_Init  usb_device.o
    0x08001788   0x08001788   0x00000002   Code   RO          229    i.MemManage_Handler  stm32f1xx_it.o
    0x0800178a   0x0800178a   0x00000002   Code   RO          230    i.NMI_Handler       stm32f1xx_it.o
    0x0800178c   0x0800178c   0x000003a6   Code   RO          783    i.PCD_EP_ISR_Handler  stm32f1xx_hal_pcd.o
    0x08001b32   0x08001b32   0x00000002   Code   RO          231    i.PendSV_Handler    stm32f1xx_it.o
    0x08001b34   0x08001b34   0x00000002   Code   RO          232    i.SVC_Handler       stm32f1xx_it.o
    0x08001b36   0x08001b36   0x00000004   Code   RO          233    i.SysTick_Handler   stm32f1xx_it.o
    0x08001b3a   0x08001b3a   0x00000076   Code   RO           15    i.SystemClock_Config  main.o
    0x08001bb0   0x08001bb0   0x00000002   Code   RO         2190    i.SystemInit        system_stm32f1xx.o
    0x08001bb2   0x08001bb2   0x0000000e   Code   RO         2490    i.USBD_CUSTOM_HID_DataIn  usbd_customhid.o
    0x08001bc0   0x08001bc0   0x00000024   Code   RO         2491    i.USBD_CUSTOM_HID_DataOut  usbd_customhid.o
    0x08001be4   0x08001be4   0x00000038   Code   RO         2492    i.USBD_CUSTOM_HID_DeInit  usbd_customhid.o
    0x08001c1c   0x08001c1c   0x00000022   Code   RO         2493    i.USBD_CUSTOM_HID_EP0_RxReady  usbd_customhid.o
    0x08001c3e   0x08001c3e   0x00000002   PAD
    0x08001c40   0x08001c40   0x0000000c   Code   RO         2494    i.USBD_CUSTOM_HID_GetDeviceQualifierDesc  usbd_customhid.o
    0x08001c4c   0x08001c4c   0x0000000c   Code   RO         2495    i.USBD_CUSTOM_HID_GetFSCfgDesc  usbd_customhid.o
    0x08001c58   0x08001c58   0x0000000c   Code   RO         2496    i.USBD_CUSTOM_HID_GetHSCfgDesc  usbd_customhid.o
    0x08001c64   0x08001c64   0x0000000c   Code   RO         2497    i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc  usbd_customhid.o
    0x08001c70   0x08001c70   0x00000054   Code   RO         2498    i.USBD_CUSTOM_HID_Init  usbd_customhid.o
    0x08001cc4   0x08001cc4   0x00000010   Code   RO         2499    i.USBD_CUSTOM_HID_RegisterInterface  usbd_customhid.o
    0x08001cd4   0x08001cd4   0x0000002c   Code   RO         2500    i.USBD_CUSTOM_HID_SendReport  usbd_customhid.o
    0x08001d00   0x08001d00   0x000000e0   Code   RO         2501    i.USBD_CUSTOM_HID_Setup  usbd_customhid.o
    0x08001de0   0x08001de0   0x0000000e   Code   RO         2227    i.USBD_ClrClassConfig  usbd_core.o
    0x08001dee   0x08001dee   0x00000010   Code   RO         2430    i.USBD_CtlContinueRx  usbd_ioreq.o
    0x08001dfe   0x08001dfe   0x00000010   Code   RO         2431    i.USBD_CtlContinueSendData  usbd_ioreq.o
    0x08001e0e   0x08001e0e   0x00000016   Code   RO         2365    i.USBD_CtlError     usbd_ctlreq.o
    0x08001e24   0x08001e24   0x0000001e   Code   RO         2432    i.USBD_CtlPrepareRx  usbd_ioreq.o
    0x08001e42   0x08001e42   0x00000016   Code   RO         2433    i.USBD_CtlReceiveStatus  usbd_ioreq.o
    0x08001e58   0x08001e58   0x0000001a   Code   RO         2434    i.USBD_CtlSendData  usbd_ioreq.o
    0x08001e72   0x08001e72   0x00000016   Code   RO         2435    i.USBD_CtlSendStatus  usbd_ioreq.o
    0x08001e88   0x08001e88   0x00000028   Code   RO          373    i.USBD_FS_ConfigStrDescriptor  usbd_desc.o
    0x08001eb0   0x08001eb0   0x0000000c   Code   RO          374    i.USBD_FS_DeviceDescriptor  usbd_desc.o
    0x08001ebc   0x08001ebc   0x0000002c   Code   RO          375    i.USBD_FS_InterfaceStrDescriptor  usbd_desc.o
    0x08001ee8   0x08001ee8   0x0000000c   Code   RO          376    i.USBD_FS_LangIDStrDescriptor  usbd_desc.o
    0x08001ef4   0x08001ef4   0x00000020   Code   RO          377    i.USBD_FS_ManufacturerStrDescriptor  usbd_desc.o
    0x08001f14   0x08001f14   0x00000034   Code   RO          378    i.USBD_FS_ProductStrDescriptor  usbd_desc.o
    0x08001f48   0x08001f48   0x00000038   Code   RO          379    i.USBD_FS_SerialStrDescriptor  usbd_desc.o
    0x08001f80   0x08001f80   0x000000e8   Code   RO         2366    i.USBD_GetDescriptor  usbd_ctlreq.o
    0x08002068   0x08002068   0x00000048   Code   RO         2367    i.USBD_GetString    usbd_ctlreq.o
    0x080020b0   0x080020b0   0x00000018   Code   RO          513    i.USBD_Get_USB_Status  usbd_conf.o
    0x080020c8   0x080020c8   0x0000002a   Code   RO         2229    i.USBD_Init         usbd_core.o
    0x080020f2   0x080020f2   0x00000012   Code   RO          514    i.USBD_LL_ClearStallEP  usbd_conf.o
    0x08002104   0x08002104   0x00000012   Code   RO          515    i.USBD_LL_CloseEP   usbd_conf.o
    0x08002116   0x08002116   0x000000c6   Code   RO         2230    i.USBD_LL_DataInStage  usbd_core.o
    0x080021dc   0x080021dc   0x00000082   Code   RO         2231    i.USBD_LL_DataOutStage  usbd_core.o
    0x0800225e   0x0800225e   0x00000002   PAD
    0x08002260   0x08002260   0x00000070   Code   RO          520    i.USBD_LL_Init      usbd_conf.o
    0x080022d0   0x080022d0   0x0000001e   Code   RO          521    i.USBD_LL_IsStallEP  usbd_conf.o
    0x080022ee   0x080022ee   0x00000018   Code   RO          522    i.USBD_LL_OpenEP    usbd_conf.o
    0x08002306   0x08002306   0x00000012   Code   RO          523    i.USBD_LL_PrepareReceive  usbd_conf.o
    0x08002318   0x08002318   0x00000050   Code   RO         2236    i.USBD_LL_Reset     usbd_core.o
    0x08002368   0x08002368   0x00000014   Code   RO         2237    i.USBD_LL_Resume    usbd_core.o
    0x0800237c   0x0800237c   0x00000018   Code   RO         2238    i.USBD_LL_SOF       usbd_core.o
    0x08002394   0x08002394   0x00000006   Code   RO         2239    i.USBD_LL_SetSpeed  usbd_core.o
    0x0800239a   0x0800239a   0x00000012   Code   RO          524    i.USBD_LL_SetUSBAddress  usbd_conf.o
    0x080023ac   0x080023ac   0x0000005a   Code   RO         2240    i.USBD_LL_SetupStage  usbd_core.o
    0x08002406   0x08002406   0x00000012   Code   RO          525    i.USBD_LL_StallEP   usbd_conf.o
    0x08002418   0x08002418   0x00000012   Code   RO          526    i.USBD_LL_Start     usbd_conf.o
    0x0800242a   0x0800242a   0x00000012   Code   RO         2241    i.USBD_LL_Suspend   usbd_core.o
    0x0800243c   0x0800243c   0x00000012   Code   RO          528    i.USBD_LL_Transmit  usbd_conf.o
    0x0800244e   0x0800244e   0x00000028   Code   RO         2368    i.USBD_ParseSetupRequest  usbd_ctlreq.o
    0x08002476   0x08002476   0x00000010   Code   RO         2242    i.USBD_RegisterClass  usbd_core.o
    0x08002486   0x08002486   0x00000016   Code   RO         2244    i.USBD_SetClassConfig  usbd_core.o
    0x0800249c   0x0800249c   0x00000084   Code   RO         2369    i.USBD_SetConfig    usbd_ctlreq.o
    0x08002520   0x08002520   0x0000000a   Code   RO         2245    i.USBD_Start        usbd_core.o
    0x0800252a   0x0800252a   0x00000112   Code   RO         2370    i.USBD_StdDevReq    usbd_ctlreq.o
    0x0800263c   0x0800263c   0x00000150   Code   RO         2371    i.USBD_StdEPReq     usbd_ctlreq.o
    0x0800278c   0x0800278c   0x0000004e   Code   RO         2372    i.USBD_StdItfReq    usbd_ctlreq.o
    0x080027da   0x080027da   0x00000002   Code   RO          529    i.USBD_static_free  usbd_conf.o
    0x080027dc   0x080027dc   0x00000008   Code   RO          530    i.USBD_static_malloc  usbd_conf.o
    0x080027e4   0x080027e4   0x0000029c   Code   RO         1026    i.USB_ActivateEndpoint  stm32f1xx_ll_usb.o
    0x08002a80   0x08002a80   0x0000000e   Code   RO         1027    i.USB_ActivateRemoteWakeup  stm32f1xx_ll_usb.o
    0x08002a8e   0x08002a8e   0x00000004   Code   RO         1029    i.USB_CoreInit      stm32f1xx_ll_usb.o
    0x08002a92   0x08002a92   0x0000000e   Code   RO         1030    i.USB_DeActivateRemoteWakeup  stm32f1xx_ll_usb.o
    0x08002aa0   0x08002aa0   0x0000010e   Code   RO         1031    i.USB_DeactivateEndpoint  stm32f1xx_ll_usb.o
    0x08002bae   0x08002bae   0x00000004   Code   RO         1032    i.USB_DevConnect    stm32f1xx_ll_usb.o
    0x08002bb2   0x08002bb2   0x00000004   Code   RO         1033    i.USB_DevDisconnect  stm32f1xx_ll_usb.o
    0x08002bb6   0x08002bb6   0x00000012   Code   RO         1034    i.USB_DevInit       stm32f1xx_ll_usb.o
    0x08002bc8   0x08002bc8   0x0000000e   Code   RO         1035    i.USB_DisableGlobalInt  stm32f1xx_ll_usb.o
    0x08002bd6   0x08002bd6   0x00000004   Code   RO         1036    i.USB_EP0_OutStart  stm32f1xx_ll_usb.o
    0x08002bda   0x08002bda   0x00000078   Code   RO         1037    i.USB_EPClearStall  stm32f1xx_ll_usb.o
    0x08002c52   0x08002c52   0x00000032   Code   RO         1038    i.USB_EPSetStall    stm32f1xx_ll_usb.o
    0x08002c84   0x08002c84   0x000004e2   Code   RO         1039    i.USB_EPStartXfer   stm32f1xx_ll_usb.o
    0x08003166   0x08003166   0x00000012   Code   RO         1041    i.USB_EnableGlobalInt  stm32f1xx_ll_usb.o
    0x08003178   0x08003178   0x00000058   Code   RO           16    i.USB_IO_rest       main.o
    0x080031d0   0x080031d0   0x0000000c   Code   RO          234    i.USB_LP_CAN1_RX0_IRQHandler  stm32f1xx_it.o
    0x080031dc   0x080031dc   0x0000008c   Code   RO           17    i.USB_MonitorStatus  main.o
    0x08003268   0x08003268   0x00000006   Code   RO         1048    i.USB_ReadInterrupts  stm32f1xx_ll_usb.o
    0x0800326e   0x0800326e   0x00000002   PAD
    0x08003270   0x08003270   0x0000002c   Code   RO         1049    i.USB_ReadPMA       stm32f1xx_ll_usb.o
    0x0800329c   0x0800329c   0x00000004   Code   RO         1050    i.USB_SetCurrentMode  stm32f1xx_ll_usb.o
    0x080032a0   0x080032a0   0x0000000c   Code   RO         1051    i.USB_SetDevAddress  stm32f1xx_ll_usb.o
    0x080032ac   0x080032ac   0x0000003c   Code   RO           18    i.USB_WakeupDevice  main.o
    0x080032e8   0x080032e8   0x00000026   Code   RO         1053    i.USB_WritePMA      stm32f1xx_ll_usb.o
    0x0800330e   0x0800330e   0x00000002   Code   RO          235    i.UsageFault_Handler  stm32f1xx_it.o
    0x08003310   0x08003310   0x00000020   Code   RO         1702    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08003330   0x08003330   0x000000c8   Code   RO           19    i.main              main.o
    0x080033f8   0x080033f8   0x00000012   Data   RO         1400    .constdata          stm32f1xx_hal_rcc.o
    0x0800340a   0x0800340a   0x00000010   Data   RO         2191    .constdata          system_stm32f1xx.o
    0x0800341a   0x0800341a   0x00000002   PAD
    0x0800341c   0x0800341c   0x00000020   Data   RO         2743    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800343c, Size: 0x00001008, Max: 0x00005000, ABSOLUTE, COMPRESSED[0x000000c4])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000048   Data   RW           21    .data               main.o
    0x20000048   COMPRESSED   0x00000001   Data   RW           22    .data               main.o
    0x20000049   COMPRESSED   0x00000003   PAD
    0x2000004c   COMPRESSED   0x0000001c   Data   RW          381    .data               usbd_desc.o
    0x20000068   COMPRESSED   0x00000032   Data   RW          382    .data               usbd_desc.o
    0x2000009a   COMPRESSED   0x00000002   PAD
    0x2000009c   COMPRESSED   0x00000019   Data   RW          461    .data               usbd_custom_hid_if.o
    0x200000b5   COMPRESSED   0x00000003   PAD
    0x200000b8   COMPRESSED   0x00000010   Data   RW          462    .data               usbd_custom_hid_if.o
    0x200000c8   COMPRESSED   0x0000000c   Data   RW         1237    .data               stm32f1xx_hal.o
    0x200000d4   COMPRESSED   0x00000004   Data   RW         2193    .data               system_stm32f1xx.o
    0x200000d8   COMPRESSED   0x00000001   Data   RW         2373    .data               usbd_ctlreq.o
    0x200000d9   COMPRESSED   0x00000003   PAD
    0x200000dc   COMPRESSED   0x00000038   Data   RW         2502    .data               usbd_customhid.o
    0x20000114   COMPRESSED   0x0000009a   Data   RW         2503    .data               usbd_customhid.o
    0x200001ae   COMPRESSED   0x00000002   PAD
    0x200001b0        -       0x000002c4   Zero   RW          338    .bss                usb_device.o
    0x20000474        -       0x00000200   Zero   RW          380    .bss                usbd_desc.o
    0x20000674        -       0x00000330   Zero   RW          531    .bss                usbd_conf.o
    0x200009a4        -       0x00000060   Zero   RW         2615    .bss                c_w.l(libspace.o)
    0x20000a04   COMPRESSED   0x00000004   PAD
    0x20000a08        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f103xb.o
    0x20000c08        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       188         18          0          0          0        943   gpio.o
       622         56          0         73          0     404708   main.o
        64         26        236          0       1536        808   startup_stm32f103xb.o
       164         28          0         12          0       5877   stm32f1xx_hal.o
       198         14          0          0          0      28947   stm32f1xx_hal_cortex.o
       514         40          0          0          0       3471   stm32f1xx_hal_gpio.o
        60          8          0          0          0        870   stm32f1xx_hal_msp.o
      3088          0          0          0          0      15404   stm32f1xx_hal_pcd.o
        60          0          0          0          0       1166   stm32f1xx_hal_pcd_ex.o
      1176         60         18          0          0       3974   stm32f1xx_hal_rcc.o
       236         12          0          0          0       1380   stm32f1xx_hal_rcc_ex.o
        38          6          0          0          0       4722   stm32f1xx_it.o
      2556          0          0          0          0      17207   stm32f1xx_ll_usb.o
         2          0         16          4          0       1107   system_stm32f1xx.o
        84         18          0          0        708        788   usb_device.o
       530         28          0          0        816      13601   usbd_conf.o
       670          0          0          0          0       9419   usbd_core.o
      1186         28          0          1          0       7888   usbd_ctlreq.o
        48         14          0         41          0       2238   usbd_custom_hid_if.o
       556         32          0        210          0      10735   usbd_customhid.o
       304        120          0         78        512       5913   usbd_desc.o
       132          0          0          0          0       4304   usbd_ioreq.o

    ----------------------------------------------------------------------
     12494        <USER>        <GROUP>        432       3572     545470   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          2         13          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        86          0          0          0          0         76   strncpy.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       574         <USER>          <GROUP>          0        100        808   Library Totals
         6          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       568         16          0          0         96        808   c_w.l

    ----------------------------------------------------------------------
       574         <USER>          <GROUP>          0        100        808   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13068        524        304        432       3672     538630   Grand Totals
     13068        524        304        196       3672     538630   ELF Image Totals (compressed)
     13068        524        304        196          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13372 (  13.06kB)
    Total RW  Size (RW Data + ZI Data)              4104 (   4.01kB)
    Total ROM Size (Code + RO Data + RW Data)      13568 (  13.25kB)

==============================================================================

