Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) for USB_LP_CAN1_RX0_IRQHandler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    main.o(i.HAL_GPIO_EXTI_Callback) refers to main.o(.data) for .data
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(i.USB_IO_rest) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.USB_IO_rest) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to main.o(i.USB_IO_rest) for USB_IO_rest
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usb_device.o(i.MX_USB_DEVICE_Init) for MX_USB_DEVICE_Init
    main.o(i.main) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.main) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_SendReport) for USBD_CUSTOM_HID_SendReport
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to usb_device.o(.bss) for hUsbDeviceFS
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f1xx_it.o(i.EXTI0_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) for HAL_PCD_IRQHandler
    stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to usbd_conf.o(.bss) for hpcd_USB_FS
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_core.o(i.USBD_Init) for USBD_Init
    usb_device.o(i.MX_USB_DEVICE_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_core.o(i.USBD_RegisterClass) for USBD_RegisterClass
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_RegisterInterface) for USBD_CUSTOM_HID_RegisterInterface
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_core.o(i.USBD_Start) for USBD_Start
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_desc.o(.data) for FS_Desc
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usb_device.o(.bss) for .bss
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_customhid.o(.data) for USBD_CUSTOM_HID
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_custom_hid_if.o(.data) for USBD_CustomHID_fops_FS
    usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) refers to usbd_desc.o(.bss) for .bss
    usbd_desc.o(i.USBD_FS_DeviceDescriptor) refers to usbd_desc.o(.data) for .data
    usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) refers to usbd_desc.o(.bss) for .bss
    usbd_desc.o(i.USBD_FS_LangIDStrDescriptor) refers to usbd_desc.o(.data) for .data
    usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) refers to usbd_desc.o(.bss) for .bss
    usbd_desc.o(i.USBD_FS_ProductStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_ProductStrDescriptor) refers to usbd_desc.o(.bss) for .bss
    usbd_desc.o(i.USBD_FS_SerialStrDescriptor) refers to usbd_desc.o(i.IntToUnicode) for IntToUnicode
    usbd_desc.o(i.USBD_FS_SerialStrDescriptor) refers to usbd_desc.o(.data) for .data
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_DeviceDescriptor) for USBD_FS_DeviceDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_LangIDStrDescriptor) for USBD_FS_LangIDStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) for USBD_FS_ManufacturerStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_ProductStrDescriptor) for USBD_FS_ProductStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_SerialStrDescriptor) for USBD_FS_SerialStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) for USBD_FS_ConfigStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) for USBD_FS_InterfaceStrDescriptor
    usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) refers to strncpy.o(.text) for strncpy
    usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) refers to usb_device.o(.bss) for hUsbDeviceFS
    usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) refers to main.o(.data) for usb_rx_buffer
    usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) refers to main.o(.data) for usb_rx_flag
    usbd_custom_hid_if.o(.data) refers to usbd_custom_hid_if.o(.data) for CUSTOM_HID_ReportDesc_FS
    usbd_custom_hid_if.o(.data) refers to usbd_custom_hid_if.o(i.CUSTOM_HID_Init_FS) for CUSTOM_HID_Init_FS
    usbd_custom_hid_if.o(.data) refers to usbd_custom_hid_if.o(i.CUSTOM_HID_DeInit_FS) for CUSTOM_HID_DeInit_FS
    usbd_custom_hid_if.o(.data) refers to usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) for CUSTOM_HID_OutEvent_FS
    usbd_conf.o(i.HAL_PCD_ConnectCallback) refers to usbd_core.o(i.USBD_LL_DevConnected) for USBD_LL_DevConnected
    usbd_conf.o(i.HAL_PCD_DataInStageCallback) refers to usbd_core.o(i.USBD_LL_DataInStage) for USBD_LL_DataInStage
    usbd_conf.o(i.HAL_PCD_DataOutStageCallback) refers to usbd_core.o(i.USBD_LL_DataOutStage) for USBD_LL_DataOutStage
    usbd_conf.o(i.HAL_PCD_DisconnectCallback) refers to usbd_core.o(i.USBD_LL_DevDisconnected) for USBD_LL_DevDisconnected
    usbd_conf.o(i.HAL_PCD_ISOINIncompleteCallback) refers to usbd_core.o(i.USBD_LL_IsoINIncomplete) for USBD_LL_IsoINIncomplete
    usbd_conf.o(i.HAL_PCD_ISOOUTIncompleteCallback) refers to usbd_core.o(i.USBD_LL_IsoOUTIncomplete) for USBD_LL_IsoOUTIncomplete
    usbd_conf.o(i.HAL_PCD_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usbd_conf.o(i.HAL_PCD_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usbd_conf.o(i.HAL_PCD_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usbd_conf.o(i.HAL_PCD_ResetCallback) refers to main.o(i.Error_Handler) for Error_Handler
    usbd_conf.o(i.HAL_PCD_ResetCallback) refers to usbd_core.o(i.USBD_LL_SetSpeed) for USBD_LL_SetSpeed
    usbd_conf.o(i.HAL_PCD_ResetCallback) refers to usbd_core.o(i.USBD_LL_Reset) for USBD_LL_Reset
    usbd_conf.o(i.HAL_PCD_ResumeCallback) refers to usbd_core.o(i.USBD_LL_Resume) for USBD_LL_Resume
    usbd_conf.o(i.HAL_PCD_SOFCallback) refers to usbd_core.o(i.USBD_LL_SOF) for USBD_LL_SOF
    usbd_conf.o(i.HAL_PCD_SetupStageCallback) refers to usbd_core.o(i.USBD_LL_SetupStage) for USBD_LL_SetupStage
    usbd_conf.o(i.HAL_PCD_SuspendCallback) refers to usbd_core.o(i.USBD_LL_Suspend) for USBD_LL_Suspend
    usbd_conf.o(i.USBD_LL_ClearStallEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall) for HAL_PCD_EP_ClrStall
    usbd_conf.o(i.USBD_LL_ClearStallEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_CloseEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Close) for HAL_PCD_EP_Close
    usbd_conf.o(i.USBD_LL_CloseEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_DeInit) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_DeInit) for HAL_PCD_DeInit
    usbd_conf.o(i.USBD_LL_DeInit) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_Delay) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    usbd_conf.o(i.USBD_LL_FlushEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Flush) for HAL_PCD_EP_Flush
    usbd_conf.o(i.USBD_LL_FlushEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_GetRxDataSize) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_GetRxCount) for HAL_PCD_EP_GetRxCount
    usbd_conf.o(i.USBD_LL_Init) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) for HAL_PCD_Init
    usbd_conf.o(i.USBD_LL_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usbd_conf.o(i.USBD_LL_Init) refers to stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_PMAConfig) for HAL_PCDEx_PMAConfig
    usbd_conf.o(i.USBD_LL_Init) refers to usbd_conf.o(.bss) for .bss
    usbd_conf.o(i.USBD_LL_OpenEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Open) for HAL_PCD_EP_Open
    usbd_conf.o(i.USBD_LL_OpenEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_PrepareReceive) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Receive) for HAL_PCD_EP_Receive
    usbd_conf.o(i.USBD_LL_PrepareReceive) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_SetUSBAddress) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress) for HAL_PCD_SetAddress
    usbd_conf.o(i.USBD_LL_SetUSBAddress) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_StallEP) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall) for HAL_PCD_EP_SetStall
    usbd_conf.o(i.USBD_LL_StallEP) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_Start) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_Start) for HAL_PCD_Start
    usbd_conf.o(i.USBD_LL_Start) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_Stop) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop) for HAL_PCD_Stop
    usbd_conf.o(i.USBD_LL_Stop) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_LL_Transmit) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Transmit) for HAL_PCD_EP_Transmit
    usbd_conf.o(i.USBD_LL_Transmit) refers to usbd_conf.o(i.USBD_Get_USB_Status) for USBD_Get_USB_Status
    usbd_conf.o(i.USBD_static_malloc) refers to usbd_conf.o(.bss) for .bss
    stm32f1xx_hal_pcd.o(i.HAL_PCD_ActivateRemoteWakeup) refers to stm32f1xx_ll_usb.o(i.USB_ActivateRemoteWakeup) for USB_ActivateRemoteWakeup
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DeActivateRemoteWakeup) refers to stm32f1xx_ll_usb.o(i.USB_DeActivateRemoteWakeup) for USB_DeActivateRemoteWakeup
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DeInit) refers to stm32f1xx_ll_usb.o(i.USB_StopDevice) for USB_StopDevice
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DeInit) refers to usbd_conf.o(i.HAL_PCD_MspDeInit) for HAL_PCD_MspDeInit
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DevConnect) refers to usbd_conf.o(i.HAL_PCDEx_SetConnectionState) for HAL_PCDEx_SetConnectionState
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DevConnect) refers to stm32f1xx_ll_usb.o(i.USB_DevConnect) for USB_DevConnect
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DevDisconnect) refers to usbd_conf.o(i.HAL_PCDEx_SetConnectionState) for HAL_PCDEx_SetConnectionState
    stm32f1xx_hal_pcd.o(i.HAL_PCD_DevDisconnect) refers to stm32f1xx_ll_usb.o(i.USB_DevDisconnect) for USB_DevDisconnect
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Abort) refers to stm32f1xx_ll_usb.o(i.USB_EPStopXfer) for USB_EPStopXfer
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Close) refers to stm32f1xx_ll_usb.o(i.USB_DeactivateEndpoint) for USB_DeactivateEndpoint
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall) refers to stm32f1xx_ll_usb.o(i.USB_EPClearStall) for USB_EPClearStall
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Receive) refers to stm32f1xx_ll_usb.o(i.USB_ReadPMA) for USB_ReadPMA
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit) refers to usbd_conf.o(i.HAL_PCD_DataInStageCallback) for HAL_PCD_DataInStageCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit) refers to stm32f1xx_ll_usb.o(i.USB_WritePMA) for USB_WritePMA
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Flush) refers to stm32f1xx_ll_usb.o(i.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Flush) refers to stm32f1xx_ll_usb.o(i.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Open) refers to stm32f1xx_ll_usb.o(i.USB_ActivateEndpoint) for USB_ActivateEndpoint
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Receive) refers to stm32f1xx_ll_usb.o(i.USB_EPStartXfer) for USB_EPStartXfer
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall) refers to stm32f1xx_ll_usb.o(i.USB_EPSetStall) for USB_EPSetStall
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall) refers to stm32f1xx_ll_usb.o(i.USB_EP0_OutStart) for USB_EP0_OutStart
    stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Transmit) refers to stm32f1xx_ll_usb.o(i.USB_EPStartXfer) for USB_EPStartXfer
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f1xx_ll_usb.o(i.USB_ReadInterrupts) for USB_ReadInterrupts
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) for PCD_EP_ISR_Handler
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_ResetCallback) for HAL_PCD_ResetCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress) for HAL_PCD_SetAddress
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_ResumeCallback) for HAL_PCD_ResumeCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_SuspendCallback) for HAL_PCD_SuspendCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_SOFCallback) for HAL_PCD_SOFCallback
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to usbd_conf.o(i.HAL_PCD_MspInit) for HAL_PCD_MspInit
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_CoreInit) for USB_CoreInit
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_SetCurrentMode) for USB_SetCurrentMode
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_DevInit) for USB_DevInit
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f1xx_ll_usb.o(i.USB_DevDisconnect) for USB_DevDisconnect
    stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress) refers to stm32f1xx_ll_usb.o(i.USB_SetDevAddress) for USB_SetDevAddress
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Start) refers to stm32f1xx_ll_usb.o(i.USB_EnableGlobalInt) for USB_EnableGlobalInt
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Start) refers to usbd_conf.o(i.HAL_PCDEx_SetConnectionState) for HAL_PCDEx_SetConnectionState
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Start) refers to stm32f1xx_ll_usb.o(i.USB_DevConnect) for USB_DevConnect
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop) refers to stm32f1xx_ll_usb.o(i.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop) refers to usbd_conf.o(i.HAL_PCDEx_SetConnectionState) for HAL_PCDEx_SetConnectionState
    stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop) refers to stm32f1xx_ll_usb.o(i.USB_DevDisconnect) for USB_DevDisconnect
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to usbd_conf.o(i.HAL_PCD_DataInStageCallback) for HAL_PCD_DataInStageCallback
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to stm32f1xx_ll_usb.o(i.USB_ReadPMA) for USB_ReadPMA
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to usbd_conf.o(i.HAL_PCD_SetupStageCallback) for HAL_PCD_SetupStageCallback
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to usbd_conf.o(i.HAL_PCD_DataOutStageCallback) for HAL_PCD_DataOutStageCallback
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Receive) for HAL_PCD_EP_DB_Receive
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to stm32f1xx_ll_usb.o(i.USB_EPStartXfer) for USB_EPStartXfer
    stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler) refers to stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit) for HAL_PCD_EP_DB_Transmit
    stm32f1xx_ll_usb.o(i.USB_EPStartXfer) refers to stm32f1xx_ll_usb.o(i.USB_WritePMA) for USB_WritePMA
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to main.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    usbd_core.o(i.USBD_DeInit) refers to usbd_conf.o(i.USBD_LL_Stop) for USBD_LL_Stop
    usbd_core.o(i.USBD_DeInit) refers to usbd_conf.o(i.USBD_LL_DeInit) for USBD_LL_DeInit
    usbd_core.o(i.USBD_Init) refers to usbd_conf.o(i.USBD_LL_Init) for USBD_LL_Init
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_ioreq.o(i.USBD_CtlContinueSendData) for USBD_CtlContinueSendData
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_ioreq.o(i.USBD_CtlReceiveStatus) for USBD_CtlReceiveStatus
    usbd_core.o(i.USBD_LL_DataOutStage) refers to usbd_ioreq.o(i.USBD_CtlContinueRx) for USBD_CtlContinueRx
    usbd_core.o(i.USBD_LL_DataOutStage) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_core.o(i.USBD_LL_DataOutStage) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_core.o(i.USBD_LL_Reset) refers to usbd_conf.o(i.USBD_LL_OpenEP) for USBD_LL_OpenEP
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_ParseSetupRequest) for USBD_ParseSetupRequest
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_StdDevReq) for USBD_StdDevReq
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_StdItfReq) for USBD_StdItfReq
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_StdEPReq) for USBD_StdEPReq
    usbd_core.o(i.USBD_Start) refers to usbd_conf.o(i.USBD_LL_Start) for USBD_LL_Start
    usbd_core.o(i.USBD_Stop) refers to usbd_conf.o(i.USBD_LL_Stop) for USBD_LL_Stop
    usbd_ctlreq.o(i.USBD_CtlError) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(i.USBD_GetDescriptor) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_GetDescriptor) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_GetDescriptor) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_core.o(i.USBD_ClrClassConfig) for USBD_ClrClassConfig
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_core.o(i.USBD_SetClassConfig) for USBD_SetClassConfig
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_ctlreq.o(.data) for .data
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_GetDescriptor) for USBD_GetDescriptor
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_conf.o(i.USBD_LL_SetUSBAddress) for USBD_LL_SetUSBAddress
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_SetConfig) for USBD_SetConfig
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_conf.o(i.USBD_LL_ClearStallEP) for USBD_LL_ClearStallEP
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_conf.o(i.USBD_LL_IsStallEP) for USBD_LL_IsStallEP
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_StdItfReq) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_StdItfReq) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ioreq.o(i.USBD_CtlContinueRx) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(i.USBD_CtlContinueSendData) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(i.USBD_CtlPrepareRx) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(i.USBD_CtlReceiveStatus) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(i.USBD_CtlSendData) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(i.USBD_CtlSendStatus) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(i.USBD_GetRxCount) refers to usbd_conf.o(i.USBD_LL_GetRxDataSize) for USBD_LL_GetRxDataSize
    usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit) refers to usbd_conf.o(i.USBD_LL_CloseEP) for USBD_LL_CloseEP
    usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit) refers to usbd_conf.o(i.USBD_static_free) for USBD_static_free
    usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(i.USBD_CUSTOM_HID_Init) refers to usbd_conf.o(i.USBD_LL_OpenEP) for USBD_LL_OpenEP
    usbd_customhid.o(i.USBD_CUSTOM_HID_Init) refers to usbd_conf.o(i.USBD_static_malloc) for USBD_static_malloc
    usbd_customhid.o(i.USBD_CUSTOM_HID_Init) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_customhid.o(i.USBD_CUSTOM_HID_SendReport) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) refers to usbd_ioreq.o(i.USBD_CtlPrepareRx) for USBD_CtlPrepareRx
    usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) refers to usbd_customhid.o(.data) for .data
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_Init) for USBD_CUSTOM_HID_Init
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit) for USBD_CUSTOM_HID_DeInit
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) for USBD_CUSTOM_HID_Setup
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_EP0_RxReady) for USBD_CUSTOM_HID_EP0_RxReady
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_DataIn) for USBD_CUSTOM_HID_DataIn
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut) for USBD_CUSTOM_HID_DataOut
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc) for USBD_CUSTOM_HID_GetHSCfgDesc
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc) for USBD_CUSTOM_HID_GetFSCfgDesc
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) for USBD_CUSTOM_HID_GetOtherSpeedCfgDesc
    usbd_customhid.o(.data) refers to usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc) for USBD_CUSTOM_HID_GetDeviceQualifierDesc
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.bss), (64 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing usb_device.o(.rev16_text), (4 bytes).
    Removing usb_device.o(.revsh_text), (4 bytes).
    Removing usb_device.o(.rrx_text), (6 bytes).
    Removing usbd_desc.o(.rev16_text), (4 bytes).
    Removing usbd_desc.o(.revsh_text), (4 bytes).
    Removing usbd_desc.o(.rrx_text), (6 bytes).
    Removing usbd_custom_hid_if.o(.rev16_text), (4 bytes).
    Removing usbd_custom_hid_if.o(.revsh_text), (4 bytes).
    Removing usbd_custom_hid_if.o(.rrx_text), (6 bytes).
    Removing usbd_conf.o(.rev16_text), (4 bytes).
    Removing usbd_conf.o(.revsh_text), (4 bytes).
    Removing usbd_conf.o(.rrx_text), (6 bytes).
    Removing usbd_conf.o(i.HAL_PCD_ConnectCallback), (8 bytes).
    Removing usbd_conf.o(i.HAL_PCD_DisconnectCallback), (8 bytes).
    Removing usbd_conf.o(i.HAL_PCD_ISOINIncompleteCallback), (8 bytes).
    Removing usbd_conf.o(i.HAL_PCD_ISOOUTIncompleteCallback), (8 bytes).
    Removing usbd_conf.o(i.HAL_PCD_MspDeInit), (36 bytes).
    Removing usbd_conf.o(i.USBD_LL_DeInit), (18 bytes).
    Removing usbd_conf.o(i.USBD_LL_Delay), (4 bytes).
    Removing usbd_conf.o(i.USBD_LL_FlushEP), (18 bytes).
    Removing usbd_conf.o(i.USBD_LL_GetRxDataSize), (8 bytes).
    Removing usbd_conf.o(i.USBD_LL_Stop), (18 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_pcd.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pcd.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pcd.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ActivateRemoteWakeup), (6 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ConnectCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DataInStageCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DataOutStageCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DeActivateRemoteWakeup), (6 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DeInit), (38 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DevConnect), (44 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DevDisconnect), (44 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_DisconnectCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Abort), (30 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Flush), (52 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_GetRxCount), (18 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_GetState), (6 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ISOINIncompleteCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ISOOUTIncompleteCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_MspInit), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ResetCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_ResumeCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_SOFCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_SetupStageCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop), (50 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_SuspendCallback), (2 bytes).
    Removing stm32f1xx_hal_pcd.o(i.HAL_PCD_WKUP_IRQHandler), (16 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_BCD_Callback), (2 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_LPM_Callback), (2 bytes).
    Removing stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_SetConnectionState), (2 bytes).
    Removing stm32f1xx_ll_usb.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_ll_usb.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_ll_usb.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ActivateRemoteWakeup), (14 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ClearInterrupts), (2 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_DeActivateRemoteWakeup), (14 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_EPStopXfer), (86 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_FlushRxFifo), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_FlushTxFifo), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ReadDevAllInEpInterrupt), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ReadDevAllOutEpInterrupt), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ReadDevInEPInterrupt), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_ReadDevOutEPInterrupt), (4 bytes).
    Removing stm32f1xx_ll_usb.o(i.USB_StopDevice), (18 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq), (32 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq), (32 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort), (70 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT), (152 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (340 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing system_stm32f1xx.o(.constdata), (8 bytes).
    Removing usbd_core.o(.rev16_text), (4 bytes).
    Removing usbd_core.o(.revsh_text), (4 bytes).
    Removing usbd_core.o(.rrx_text), (6 bytes).
    Removing usbd_core.o(i.USBD_DeInit), (38 bytes).
    Removing usbd_core.o(i.USBD_LL_DevConnected), (4 bytes).
    Removing usbd_core.o(i.USBD_LL_DevDisconnected), (22 bytes).
    Removing usbd_core.o(i.USBD_LL_IsoINIncomplete), (4 bytes).
    Removing usbd_core.o(i.USBD_LL_IsoOUTIncomplete), (4 bytes).
    Removing usbd_core.o(i.USBD_RunTestMode), (4 bytes).
    Removing usbd_core.o(i.USBD_Stop), (26 bytes).
    Removing usbd_ctlreq.o(.rev16_text), (4 bytes).
    Removing usbd_ctlreq.o(.revsh_text), (4 bytes).
    Removing usbd_ctlreq.o(.rrx_text), (6 bytes).
    Removing usbd_ioreq.o(.rev16_text), (4 bytes).
    Removing usbd_ioreq.o(.revsh_text), (4 bytes).
    Removing usbd_ioreq.o(.rrx_text), (6 bytes).
    Removing usbd_ioreq.o(i.USBD_GetRxCount), (4 bytes).
    Removing usbd_customhid.o(.rev16_text), (4 bytes).
    Removing usbd_customhid.o(.revsh_text), (4 bytes).
    Removing usbd_customhid.o(.rrx_text), (6 bytes).

261 unused section(s) (total 7874 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pcd.c 0x00000000   Number         0  stm32f1xx_hal_pcd.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pcd_ex.c 0x00000000   Number         0  stm32f1xx_hal_pcd_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_ll_usb.c 0x00000000   Number         0  stm32f1xx_ll_usb.o ABSOLUTE
    ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Src/usbd_customhid.c 0x00000000   Number         0  usbd_customhid.o ABSOLUTE
    ../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_core.c 0x00000000   Number         0  usbd_core.o ABSOLUTE
    ../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ctlreq.c 0x00000000   Number         0  usbd_ctlreq.o ABSOLUTE
    ../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ioreq.c 0x00000000   Number         0  usbd_ioreq.o ABSOLUTE
    ../USB_DEVICE/App/usb_device.c           0x00000000   Number         0  usb_device.o ABSOLUTE
    ../USB_DEVICE/App/usbd_custom_hid_if.c   0x00000000   Number         0  usbd_custom_hid_if.o ABSOLUTE
    ../USB_DEVICE/App/usbd_desc.c            0x00000000   Number         0  usbd_desc.o ABSOLUTE
    ../USB_DEVICE/Target/usbd_conf.c         0x00000000   Number         0  usbd_conf.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pcd.c 0x00000000   Number         0  stm32f1xx_hal_pcd.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pcd_ex.c 0x00000000   Number         0  stm32f1xx_hal_pcd_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_ll_usb.c 0x00000000   Number         0  stm32f1xx_ll_usb.o ABSOLUTE
    ..\Middlewares\ST\STM32_USB_Device_Library\Class\CustomHID\Src\usbd_customhid.c 0x00000000   Number         0  usbd_customhid.o ABSOLUTE
    ..\Middlewares\ST\STM32_USB_Device_Library\Core\Src\usbd_core.c 0x00000000   Number         0  usbd_core.o ABSOLUTE
    ..\Middlewares\ST\STM32_USB_Device_Library\Core\Src\usbd_ctlreq.c 0x00000000   Number         0  usbd_ctlreq.o ABSOLUTE
    ..\Middlewares\ST\STM32_USB_Device_Library\Core\Src\usbd_ioreq.c 0x00000000   Number         0  usbd_ioreq.o ABSOLUTE
    ..\USB_DEVICE\App\usb_device.c           0x00000000   Number         0  usb_device.o ABSOLUTE
    ..\USB_DEVICE\App\usbd_custom_hid_if.c   0x00000000   Number         0  usbd_custom_hid_if.o ABSOLUTE
    ..\USB_DEVICE\App\usbd_desc.c            0x00000000   Number         0  usbd_desc.o ABSOLUTE
    ..\USB_DEVICE\Target\usbd_conf.c         0x00000000   Number         0  usbd_conf.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x08000128   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000184   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a2   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a4   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001a6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001a6   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001a8   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001a8   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001a8   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001ae   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001ae   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b2   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001ba   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001bc   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001bc   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c0   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001c8   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x08000208   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000256   Section       86  strncpy.o(.text)
    .text                                    0x080002ac   Section        0  heapauxi.o(.text)
    .text                                    0x080002b2   Section       68  rt_memclr.o(.text)
    .text                                    0x080002f6   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000340   Section        0  exit.o(.text)
    .text                                    0x08000354   Section        8  libspace.o(.text)
    .text                                    0x0800035c   Section        0  sys_exit.o(.text)
    .text                                    0x08000368   Section        2  use_no_semi.o(.text)
    .text                                    0x0800036a   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x0800036a   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.CUSTOM_HID_DeInit_FS                   0x0800036c   Section        0  usbd_custom_hid_if.o(i.CUSTOM_HID_DeInit_FS)
    CUSTOM_HID_DeInit_FS                     0x0800036d   Thumb Code     4  usbd_custom_hid_if.o(i.CUSTOM_HID_DeInit_FS)
    i.CUSTOM_HID_Init_FS                     0x08000370   Section        0  usbd_custom_hid_if.o(i.CUSTOM_HID_Init_FS)
    CUSTOM_HID_Init_FS                       0x08000371   Thumb Code     4  usbd_custom_hid_if.o(i.CUSTOM_HID_Init_FS)
    i.CUSTOM_HID_OutEvent_FS                 0x08000374   Section        0  usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS)
    CUSTOM_HID_OutEvent_FS                   0x08000375   Thumb Code    26  usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS)
    i.DebugMon_Handler                       0x0800039c   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.EXTI0_IRQHandler                       0x0800039e   Section        0  stm32f1xx_it.o(i.EXTI0_IRQHandler)
    i.Error_Handler                          0x080003a4   Section        0  main.o(i.Error_Handler)
    i.HAL_GPIO_EXTI_Callback                 0x080003a8   Section        0  main.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x080003b4   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x080003cc   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x080005ac   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080005b8   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x080005c4   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080005d4   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080005f8   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000638   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000674   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000690   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080006d0   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PCDEx_PMAConfig                    0x080006f4   Section        0  stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_PMAConfig)
    i.HAL_PCDEx_SetConnectionState           0x08000730   Section        0  usbd_conf.o(i.HAL_PCDEx_SetConnectionState)
    i.HAL_PCD_DataInStageCallback            0x08000732   Section        0  usbd_conf.o(i.HAL_PCD_DataInStageCallback)
    i.HAL_PCD_DataOutStageCallback           0x08000744   Section        0  usbd_conf.o(i.HAL_PCD_DataOutStageCallback)
    i.HAL_PCD_EP_Close                       0x08000758   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Close)
    i.HAL_PCD_EP_ClrStall                    0x080007a2   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall)
    i.HAL_PCD_EP_DB_Receive                  0x080007fc   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Receive)
    HAL_PCD_EP_DB_Receive                    0x080007fd   Thumb Code   230  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Receive)
    i.HAL_PCD_EP_DB_Transmit                 0x080008e2   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit)
    HAL_PCD_EP_DB_Transmit                   0x080008e3   Thumb Code   824  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit)
    i.HAL_PCD_EP_Open                        0x08000c1a   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Open)
    i.HAL_PCD_EP_Receive                     0x08000c72   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Receive)
    i.HAL_PCD_EP_SetStall                    0x08000ca0   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall)
    i.HAL_PCD_EP_Transmit                    0x08000d10   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Transmit)
    i.HAL_PCD_IRQHandler                     0x08000d40   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler)
    i.HAL_PCD_Init                           0x08000e74   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_Init)
    i.HAL_PCD_MspInit                        0x08000f30   Section        0  usbd_conf.o(i.HAL_PCD_MspInit)
    i.HAL_PCD_ResetCallback                  0x08000f6c   Section        0  usbd_conf.o(i.HAL_PCD_ResetCallback)
    i.HAL_PCD_ResumeCallback                 0x08000f92   Section        0  usbd_conf.o(i.HAL_PCD_ResumeCallback)
    i.HAL_PCD_SOFCallback                    0x08000f9a   Section        0  usbd_conf.o(i.HAL_PCD_SOFCallback)
    i.HAL_PCD_SetAddress                     0x08000fa2   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress)
    i.HAL_PCD_SetupStageCallback             0x08000fc8   Section        0  usbd_conf.o(i.HAL_PCD_SetupStageCallback)
    i.HAL_PCD_Start                          0x08000fd6   Section        0  stm32f1xx_hal_pcd.o(i.HAL_PCD_Start)
    i.HAL_PCD_SuspendCallback                0x08001008   Section        0  usbd_conf.o(i.HAL_PCD_SuspendCallback)
    i.HAL_RCCEx_PeriphCLKConfig              0x0800102c   Section        0  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08001118   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetSysClockFreq                0x08001244   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001290   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x080015b0   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HardFault_Handler                      0x080015d8   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.IntToUnicode                           0x080015dc   Section        0  usbd_desc.o(i.IntToUnicode)
    IntToUnicode                             0x080015dd   Thumb Code    56  usbd_desc.o(i.IntToUnicode)
    i.MX_GPIO_Init                           0x08001614   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USB_DEVICE_Init                     0x080016d0   Section        0  usb_device.o(i.MX_USB_DEVICE_Init)
    i.MemManage_Handler                      0x08001724   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001726   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.PCD_EP_ISR_Handler                     0x08001728   Section        0  stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler)
    PCD_EP_ISR_Handler                       0x08001729   Thumb Code   934  stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler)
    i.PendSV_Handler                         0x08001ace   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08001ad0   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08001ad2   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001ad6   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001b4c   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.USBD_CUSTOM_HID_DataIn                 0x08001b4e   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_DataIn)
    USBD_CUSTOM_HID_DataIn                   0x08001b4f   Thumb Code    14  usbd_customhid.o(i.USBD_CUSTOM_HID_DataIn)
    i.USBD_CUSTOM_HID_DataOut                0x08001b5c   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut)
    USBD_CUSTOM_HID_DataOut                  0x08001b5d   Thumb Code    36  usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut)
    i.USBD_CUSTOM_HID_DeInit                 0x08001b80   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit)
    USBD_CUSTOM_HID_DeInit                   0x08001b81   Thumb Code    56  usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit)
    i.USBD_CUSTOM_HID_EP0_RxReady            0x08001bb8   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_EP0_RxReady)
    USBD_CUSTOM_HID_EP0_RxReady              0x08001bb9   Thumb Code    34  usbd_customhid.o(i.USBD_CUSTOM_HID_EP0_RxReady)
    i.USBD_CUSTOM_HID_GetDeviceQualifierDesc 0x08001bdc   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc)
    USBD_CUSTOM_HID_GetDeviceQualifierDesc   0x08001bdd   Thumb Code     8  usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc)
    i.USBD_CUSTOM_HID_GetFSCfgDesc           0x08001be8   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc)
    USBD_CUSTOM_HID_GetFSCfgDesc             0x08001be9   Thumb Code     8  usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc)
    i.USBD_CUSTOM_HID_GetHSCfgDesc           0x08001bf4   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc)
    USBD_CUSTOM_HID_GetHSCfgDesc             0x08001bf5   Thumb Code     8  usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc)
    i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc   0x08001c00   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc)
    USBD_CUSTOM_HID_GetOtherSpeedCfgDesc     0x08001c01   Thumb Code     8  usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc)
    i.USBD_CUSTOM_HID_Init                   0x08001c0c   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_Init)
    USBD_CUSTOM_HID_Init                     0x08001c0d   Thumb Code    84  usbd_customhid.o(i.USBD_CUSTOM_HID_Init)
    i.USBD_CUSTOM_HID_RegisterInterface      0x08001c60   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_RegisterInterface)
    i.USBD_CUSTOM_HID_SendReport             0x08001c70   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_SendReport)
    i.USBD_CUSTOM_HID_Setup                  0x08001c9c   Section        0  usbd_customhid.o(i.USBD_CUSTOM_HID_Setup)
    USBD_CUSTOM_HID_Setup                    0x08001c9d   Thumb Code   218  usbd_customhid.o(i.USBD_CUSTOM_HID_Setup)
    i.USBD_ClrClassConfig                    0x08001d7c   Section        0  usbd_core.o(i.USBD_ClrClassConfig)
    i.USBD_CtlContinueRx                     0x08001d8a   Section        0  usbd_ioreq.o(i.USBD_CtlContinueRx)
    i.USBD_CtlContinueSendData               0x08001d9a   Section        0  usbd_ioreq.o(i.USBD_CtlContinueSendData)
    i.USBD_CtlError                          0x08001daa   Section        0  usbd_ctlreq.o(i.USBD_CtlError)
    i.USBD_CtlPrepareRx                      0x08001dc0   Section        0  usbd_ioreq.o(i.USBD_CtlPrepareRx)
    i.USBD_CtlReceiveStatus                  0x08001dde   Section        0  usbd_ioreq.o(i.USBD_CtlReceiveStatus)
    i.USBD_CtlSendData                       0x08001df4   Section        0  usbd_ioreq.o(i.USBD_CtlSendData)
    i.USBD_CtlSendStatus                     0x08001e0e   Section        0  usbd_ioreq.o(i.USBD_CtlSendStatus)
    i.USBD_FS_ConfigStrDescriptor            0x08001e24   Section        0  usbd_desc.o(i.USBD_FS_ConfigStrDescriptor)
    i.USBD_FS_DeviceDescriptor               0x08001e4c   Section        0  usbd_desc.o(i.USBD_FS_DeviceDescriptor)
    i.USBD_FS_InterfaceStrDescriptor         0x08001e58   Section        0  usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor)
    i.USBD_FS_LangIDStrDescriptor            0x08001e84   Section        0  usbd_desc.o(i.USBD_FS_LangIDStrDescriptor)
    i.USBD_FS_ManufacturerStrDescriptor      0x08001e90   Section        0  usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor)
    i.USBD_FS_ProductStrDescriptor           0x08001eb0   Section        0  usbd_desc.o(i.USBD_FS_ProductStrDescriptor)
    i.USBD_FS_SerialStrDescriptor            0x08001ee4   Section        0  usbd_desc.o(i.USBD_FS_SerialStrDescriptor)
    i.USBD_GetDescriptor                     0x08001f1c   Section        0  usbd_ctlreq.o(i.USBD_GetDescriptor)
    USBD_GetDescriptor                       0x08001f1d   Thumb Code   232  usbd_ctlreq.o(i.USBD_GetDescriptor)
    i.USBD_GetString                         0x08002004   Section        0  usbd_ctlreq.o(i.USBD_GetString)
    i.USBD_Get_USB_Status                    0x0800204c   Section        0  usbd_conf.o(i.USBD_Get_USB_Status)
    USBD_Get_USB_Status                      0x0800204d   Thumb Code    24  usbd_conf.o(i.USBD_Get_USB_Status)
    i.USBD_Init                              0x08002064   Section        0  usbd_core.o(i.USBD_Init)
    i.USBD_LL_ClearStallEP                   0x0800208e   Section        0  usbd_conf.o(i.USBD_LL_ClearStallEP)
    i.USBD_LL_CloseEP                        0x080020a0   Section        0  usbd_conf.o(i.USBD_LL_CloseEP)
    i.USBD_LL_DataInStage                    0x080020b2   Section        0  usbd_core.o(i.USBD_LL_DataInStage)
    i.USBD_LL_DataOutStage                   0x08002178   Section        0  usbd_core.o(i.USBD_LL_DataOutStage)
    i.USBD_LL_Init                           0x080021fc   Section        0  usbd_conf.o(i.USBD_LL_Init)
    i.USBD_LL_IsStallEP                      0x0800226c   Section        0  usbd_conf.o(i.USBD_LL_IsStallEP)
    i.USBD_LL_OpenEP                         0x0800228a   Section        0  usbd_conf.o(i.USBD_LL_OpenEP)
    i.USBD_LL_PrepareReceive                 0x080022a2   Section        0  usbd_conf.o(i.USBD_LL_PrepareReceive)
    i.USBD_LL_Reset                          0x080022b4   Section        0  usbd_core.o(i.USBD_LL_Reset)
    i.USBD_LL_Resume                         0x08002304   Section        0  usbd_core.o(i.USBD_LL_Resume)
    i.USBD_LL_SOF                            0x08002318   Section        0  usbd_core.o(i.USBD_LL_SOF)
    i.USBD_LL_SetSpeed                       0x08002330   Section        0  usbd_core.o(i.USBD_LL_SetSpeed)
    i.USBD_LL_SetUSBAddress                  0x08002336   Section        0  usbd_conf.o(i.USBD_LL_SetUSBAddress)
    i.USBD_LL_SetupStage                     0x08002348   Section        0  usbd_core.o(i.USBD_LL_SetupStage)
    i.USBD_LL_StallEP                        0x080023a2   Section        0  usbd_conf.o(i.USBD_LL_StallEP)
    i.USBD_LL_Start                          0x080023b4   Section        0  usbd_conf.o(i.USBD_LL_Start)
    i.USBD_LL_Suspend                        0x080023c6   Section        0  usbd_core.o(i.USBD_LL_Suspend)
    i.USBD_LL_Transmit                       0x080023d8   Section        0  usbd_conf.o(i.USBD_LL_Transmit)
    i.USBD_ParseSetupRequest                 0x080023ea   Section        0  usbd_ctlreq.o(i.USBD_ParseSetupRequest)
    i.USBD_RegisterClass                     0x08002412   Section        0  usbd_core.o(i.USBD_RegisterClass)
    i.USBD_SetClassConfig                    0x08002422   Section        0  usbd_core.o(i.USBD_SetClassConfig)
    i.USBD_SetConfig                         0x08002438   Section        0  usbd_ctlreq.o(i.USBD_SetConfig)
    USBD_SetConfig                           0x08002439   Thumb Code   128  usbd_ctlreq.o(i.USBD_SetConfig)
    i.USBD_Start                             0x080024bc   Section        0  usbd_core.o(i.USBD_Start)
    i.USBD_StdDevReq                         0x080024c6   Section        0  usbd_ctlreq.o(i.USBD_StdDevReq)
    i.USBD_StdEPReq                          0x080025d8   Section        0  usbd_ctlreq.o(i.USBD_StdEPReq)
    i.USBD_StdItfReq                         0x08002728   Section        0  usbd_ctlreq.o(i.USBD_StdItfReq)
    i.USBD_static_free                       0x08002776   Section        0  usbd_conf.o(i.USBD_static_free)
    i.USBD_static_malloc                     0x08002778   Section        0  usbd_conf.o(i.USBD_static_malloc)
    i.USB_ActivateEndpoint                   0x08002780   Section        0  stm32f1xx_ll_usb.o(i.USB_ActivateEndpoint)
    i.USB_CoreInit                           0x08002a1c   Section        0  stm32f1xx_ll_usb.o(i.USB_CoreInit)
    i.USB_DeactivateEndpoint                 0x08002a20   Section        0  stm32f1xx_ll_usb.o(i.USB_DeactivateEndpoint)
    i.USB_DevConnect                         0x08002b2e   Section        0  stm32f1xx_ll_usb.o(i.USB_DevConnect)
    i.USB_DevDisconnect                      0x08002b32   Section        0  stm32f1xx_ll_usb.o(i.USB_DevDisconnect)
    i.USB_DevInit                            0x08002b36   Section        0  stm32f1xx_ll_usb.o(i.USB_DevInit)
    i.USB_DisableGlobalInt                   0x08002b48   Section        0  stm32f1xx_ll_usb.o(i.USB_DisableGlobalInt)
    i.USB_EP0_OutStart                       0x08002b56   Section        0  stm32f1xx_ll_usb.o(i.USB_EP0_OutStart)
    i.USB_EPClearStall                       0x08002b5a   Section        0  stm32f1xx_ll_usb.o(i.USB_EPClearStall)
    i.USB_EPSetStall                         0x08002bd2   Section        0  stm32f1xx_ll_usb.o(i.USB_EPSetStall)
    i.USB_EPStartXfer                        0x08002c04   Section        0  stm32f1xx_ll_usb.o(i.USB_EPStartXfer)
    i.USB_EnableGlobalInt                    0x080030e6   Section        0  stm32f1xx_ll_usb.o(i.USB_EnableGlobalInt)
    i.USB_IO_rest                            0x080030f8   Section        0  main.o(i.USB_IO_rest)
    i.USB_LP_CAN1_RX0_IRQHandler             0x08003150   Section        0  stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler)
    i.USB_ReadInterrupts                     0x0800315c   Section        0  stm32f1xx_ll_usb.o(i.USB_ReadInterrupts)
    i.USB_ReadPMA                            0x08003164   Section        0  stm32f1xx_ll_usb.o(i.USB_ReadPMA)
    i.USB_SetCurrentMode                     0x08003190   Section        0  stm32f1xx_ll_usb.o(i.USB_SetCurrentMode)
    i.USB_SetDevAddress                      0x08003194   Section        0  stm32f1xx_ll_usb.o(i.USB_SetDevAddress)
    i.USB_WritePMA                           0x080031a0   Section        0  stm32f1xx_ll_usb.o(i.USB_WritePMA)
    i.UsageFault_Handler                     0x080031c6   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x080031c8   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080031c9   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.main                                   0x080031e8   Section        0  main.o(i.main)
    .constdata                               0x08003254   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x08003254   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x08003256   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x08003266   Section       16  system_stm32f1xx.o(.constdata)
    .data                                    0x20000000   Section       65  main.o(.data)
    .data                                    0x20000041   Section        1  main.o(.data)
    .data                                    0x20000044   Section       28  usbd_desc.o(.data)
    .data                                    0x20000060   Section       50  usbd_desc.o(.data)
    .data                                    0x20000094   Section       25  usbd_custom_hid_if.o(.data)
    CUSTOM_HID_ReportDesc_FS                 0x20000094   Data          25  usbd_custom_hid_if.o(.data)
    .data                                    0x200000b0   Section       16  usbd_custom_hid_if.o(.data)
    .data                                    0x200000c0   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x200000cc   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x200000d0   Section        1  usbd_ctlreq.o(.data)
    cfgidx                                   0x200000d0   Data           1  usbd_ctlreq.o(.data)
    .data                                    0x200000d4   Section       56  usbd_customhid.o(.data)
    .data                                    0x2000010c   Section      154  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_CfgFSDesc                0x2000010c   Data          41  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_CfgHSDesc                0x20000138   Data          41  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_OtherSpeedCfgDesc        0x20000164   Data          41  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_Desc                     0x20000190   Data           9  usbd_customhid.o(.data)
    USBD_CUSTOM_HID_DeviceQualifierDesc      0x2000019c   Data          10  usbd_customhid.o(.data)
    .bss                                     0x200001a8   Section      708  usb_device.o(.bss)
    .bss                                     0x2000046c   Section      512  usbd_desc.o(.bss)
    .bss                                     0x2000066c   Section      816  usbd_conf.o(.bss)
    mem                                      0x2000066c   Data          88  usbd_conf.o(.bss)
    .bss                                     0x2000099c   Section       96  libspace.o(.bss)
    HEAP                                     0x20000a00   Section      512  startup_stm32f103xb.o(HEAP)
    Heap_Mem                                 0x20000a00   Data         512  startup_stm32f103xb.o(HEAP)
    STACK                                    0x20000c00   Section     1024  startup_stm32f103xb.o(STACK)
    Stack_Mem                                0x20000c00   Data        1024  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x20001000   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x08000129   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x08000129   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000185   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a5   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001a7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001a9   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001a9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001a9   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001af   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001af   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bb   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001bd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001bd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001c9   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART1_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART2_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x080001e3   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x080001e5   Thumb Code     0  startup_stm32f103xb.o(.text)
    __aeabi_memclr4                          0x08000209   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000209   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000209   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800020d   Thumb Code     0  rt_memclr_w.o(.text)
    strncpy                                  0x08000257   Thumb Code    86  strncpy.o(.text)
    __use_two_region_memory                  0x080002ad   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002af   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002b1   Thumb Code     2  heapauxi.o(.text)
    __aeabi_memclr                           0x080002b3   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080002b3   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080002b7   Thumb Code     0  rt_memclr.o(.text)
    __user_setup_stackheap                   0x080002f7   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000341   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000355   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000355   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000355   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x0800035d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000369   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000369   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x0800036b   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x0800036b   Thumb Code     0  indicate_semi.o(.text)
    DebugMon_Handler                         0x0800039d   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    EXTI0_IRQHandler                         0x0800039f   Thumb Code     6  stm32f1xx_it.o(i.EXTI0_IRQHandler)
    Error_Handler                            0x080003a5   Thumb Code     4  main.o(i.Error_Handler)
    HAL_GPIO_EXTI_Callback                   0x080003a9   Thumb Code     8  main.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x080003b5   Thumb Code    18  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x080003cd   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x080005ad   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080005b9   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x080005c5   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080005d5   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080005f9   Thumb Code    54  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000639   Thumb Code    52  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000675   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000691   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080006d1   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PCDEx_PMAConfig                      0x080006f5   Thumb Code    60  stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_PMAConfig)
    HAL_PCDEx_SetConnectionState             0x08000731   Thumb Code     2  usbd_conf.o(i.HAL_PCDEx_SetConnectionState)
    HAL_PCD_DataInStageCallback              0x08000733   Thumb Code    18  usbd_conf.o(i.HAL_PCD_DataInStageCallback)
    HAL_PCD_DataOutStageCallback             0x08000745   Thumb Code    20  usbd_conf.o(i.HAL_PCD_DataOutStageCallback)
    HAL_PCD_EP_Close                         0x08000759   Thumb Code    74  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Close)
    HAL_PCD_EP_ClrStall                      0x080007a3   Thumb Code    90  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall)
    HAL_PCD_EP_Open                          0x08000c1b   Thumb Code    88  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Open)
    HAL_PCD_EP_Receive                       0x08000c73   Thumb Code    46  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Receive)
    HAL_PCD_EP_SetStall                      0x08000ca1   Thumb Code   112  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall)
    HAL_PCD_EP_Transmit                      0x08000d11   Thumb Code    46  stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Transmit)
    HAL_PCD_IRQHandler                       0x08000d41   Thumb Code   308  stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler)
    HAL_PCD_Init                             0x08000e75   Thumb Code   186  stm32f1xx_hal_pcd.o(i.HAL_PCD_Init)
    HAL_PCD_MspInit                          0x08000f31   Thumb Code    50  usbd_conf.o(i.HAL_PCD_MspInit)
    HAL_PCD_ResetCallback                    0x08000f6d   Thumb Code    38  usbd_conf.o(i.HAL_PCD_ResetCallback)
    HAL_PCD_ResumeCallback                   0x08000f93   Thumb Code     8  usbd_conf.o(i.HAL_PCD_ResumeCallback)
    HAL_PCD_SOFCallback                      0x08000f9b   Thumb Code     8  usbd_conf.o(i.HAL_PCD_SOFCallback)
    HAL_PCD_SetAddress                       0x08000fa3   Thumb Code    38  stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress)
    HAL_PCD_SetupStageCallback               0x08000fc9   Thumb Code    14  usbd_conf.o(i.HAL_PCD_SetupStageCallback)
    HAL_PCD_Start                            0x08000fd7   Thumb Code    50  stm32f1xx_hal_pcd.o(i.HAL_PCD_Start)
    HAL_PCD_SuspendCallback                  0x08001009   Thumb Code    30  usbd_conf.o(i.HAL_PCD_SuspendCallback)
    HAL_RCCEx_PeriphCLKConfig                0x0800102d   Thumb Code   224  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08001119   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetSysClockFreq                  0x08001245   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001291   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080015b1   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HardFault_Handler                        0x080015d9   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    MX_GPIO_Init                             0x08001615   Thumb Code   170  gpio.o(i.MX_GPIO_Init)
    MX_USB_DEVICE_Init                       0x080016d1   Thumb Code    66  usb_device.o(i.MX_USB_DEVICE_Init)
    MemManage_Handler                        0x08001725   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001727   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08001acf   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08001ad1   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001ad3   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001ad7   Thumb Code   118  main.o(i.SystemClock_Config)
    SystemInit                               0x08001b4d   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    USBD_CUSTOM_HID_RegisterInterface        0x08001c61   Thumb Code    16  usbd_customhid.o(i.USBD_CUSTOM_HID_RegisterInterface)
    USBD_CUSTOM_HID_SendReport               0x08001c71   Thumb Code    44  usbd_customhid.o(i.USBD_CUSTOM_HID_SendReport)
    USBD_ClrClassConfig                      0x08001d7d   Thumb Code    14  usbd_core.o(i.USBD_ClrClassConfig)
    USBD_CtlContinueRx                       0x08001d8b   Thumb Code    16  usbd_ioreq.o(i.USBD_CtlContinueRx)
    USBD_CtlContinueSendData                 0x08001d9b   Thumb Code    16  usbd_ioreq.o(i.USBD_CtlContinueSendData)
    USBD_CtlError                            0x08001dab   Thumb Code    22  usbd_ctlreq.o(i.USBD_CtlError)
    USBD_CtlPrepareRx                        0x08001dc1   Thumb Code    30  usbd_ioreq.o(i.USBD_CtlPrepareRx)
    USBD_CtlReceiveStatus                    0x08001ddf   Thumb Code    22  usbd_ioreq.o(i.USBD_CtlReceiveStatus)
    USBD_CtlSendData                         0x08001df5   Thumb Code    26  usbd_ioreq.o(i.USBD_CtlSendData)
    USBD_CtlSendStatus                       0x08001e0f   Thumb Code    22  usbd_ioreq.o(i.USBD_CtlSendStatus)
    USBD_FS_ConfigStrDescriptor              0x08001e25   Thumb Code    16  usbd_desc.o(i.USBD_FS_ConfigStrDescriptor)
    USBD_FS_DeviceDescriptor                 0x08001e4d   Thumb Code     8  usbd_desc.o(i.USBD_FS_DeviceDescriptor)
    USBD_FS_InterfaceStrDescriptor           0x08001e59   Thumb Code    16  usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor)
    USBD_FS_LangIDStrDescriptor              0x08001e85   Thumb Code     8  usbd_desc.o(i.USBD_FS_LangIDStrDescriptor)
    USBD_FS_ManufacturerStrDescriptor        0x08001e91   Thumb Code    16  usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor)
    USBD_FS_ProductStrDescriptor             0x08001eb1   Thumb Code    16  usbd_desc.o(i.USBD_FS_ProductStrDescriptor)
    USBD_FS_SerialStrDescriptor              0x08001ee5   Thumb Code    48  usbd_desc.o(i.USBD_FS_SerialStrDescriptor)
    USBD_GetString                           0x08002005   Thumb Code    72  usbd_ctlreq.o(i.USBD_GetString)
    USBD_Init                                0x08002065   Thumb Code    42  usbd_core.o(i.USBD_Init)
    USBD_LL_ClearStallEP                     0x0800208f   Thumb Code    18  usbd_conf.o(i.USBD_LL_ClearStallEP)
    USBD_LL_CloseEP                          0x080020a1   Thumb Code    18  usbd_conf.o(i.USBD_LL_CloseEP)
    USBD_LL_DataInStage                      0x080020b3   Thumb Code   198  usbd_core.o(i.USBD_LL_DataInStage)
    USBD_LL_DataOutStage                     0x08002179   Thumb Code   130  usbd_core.o(i.USBD_LL_DataOutStage)
    USBD_LL_Init                             0x080021fd   Thumb Code   104  usbd_conf.o(i.USBD_LL_Init)
    USBD_LL_IsStallEP                        0x0800226d   Thumb Code    30  usbd_conf.o(i.USBD_LL_IsStallEP)
    USBD_LL_OpenEP                           0x0800228b   Thumb Code    24  usbd_conf.o(i.USBD_LL_OpenEP)
    USBD_LL_PrepareReceive                   0x080022a3   Thumb Code    18  usbd_conf.o(i.USBD_LL_PrepareReceive)
    USBD_LL_Reset                            0x080022b5   Thumb Code    80  usbd_core.o(i.USBD_LL_Reset)
    USBD_LL_Resume                           0x08002305   Thumb Code    20  usbd_core.o(i.USBD_LL_Resume)
    USBD_LL_SOF                              0x08002319   Thumb Code    24  usbd_core.o(i.USBD_LL_SOF)
    USBD_LL_SetSpeed                         0x08002331   Thumb Code     6  usbd_core.o(i.USBD_LL_SetSpeed)
    USBD_LL_SetUSBAddress                    0x08002337   Thumb Code    18  usbd_conf.o(i.USBD_LL_SetUSBAddress)
    USBD_LL_SetupStage                       0x08002349   Thumb Code    90  usbd_core.o(i.USBD_LL_SetupStage)
    USBD_LL_StallEP                          0x080023a3   Thumb Code    18  usbd_conf.o(i.USBD_LL_StallEP)
    USBD_LL_Start                            0x080023b5   Thumb Code    18  usbd_conf.o(i.USBD_LL_Start)
    USBD_LL_Suspend                          0x080023c7   Thumb Code    18  usbd_core.o(i.USBD_LL_Suspend)
    USBD_LL_Transmit                         0x080023d9   Thumb Code    18  usbd_conf.o(i.USBD_LL_Transmit)
    USBD_ParseSetupRequest                   0x080023eb   Thumb Code    40  usbd_ctlreq.o(i.USBD_ParseSetupRequest)
    USBD_RegisterClass                       0x08002413   Thumb Code    16  usbd_core.o(i.USBD_RegisterClass)
    USBD_SetClassConfig                      0x08002423   Thumb Code    22  usbd_core.o(i.USBD_SetClassConfig)
    USBD_Start                               0x080024bd   Thumb Code    10  usbd_core.o(i.USBD_Start)
    USBD_StdDevReq                           0x080024c7   Thumb Code   274  usbd_ctlreq.o(i.USBD_StdDevReq)
    USBD_StdEPReq                            0x080025d9   Thumb Code   336  usbd_ctlreq.o(i.USBD_StdEPReq)
    USBD_StdItfReq                           0x08002729   Thumb Code    78  usbd_ctlreq.o(i.USBD_StdItfReq)
    USBD_static_free                         0x08002777   Thumb Code     2  usbd_conf.o(i.USBD_static_free)
    USBD_static_malloc                       0x08002779   Thumb Code     4  usbd_conf.o(i.USBD_static_malloc)
    USB_ActivateEndpoint                     0x08002781   Thumb Code   668  stm32f1xx_ll_usb.o(i.USB_ActivateEndpoint)
    USB_CoreInit                             0x08002a1d   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_CoreInit)
    USB_DeactivateEndpoint                   0x08002a21   Thumb Code   270  stm32f1xx_ll_usb.o(i.USB_DeactivateEndpoint)
    USB_DevConnect                           0x08002b2f   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_DevConnect)
    USB_DevDisconnect                        0x08002b33   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_DevDisconnect)
    USB_DevInit                              0x08002b37   Thumb Code    18  stm32f1xx_ll_usb.o(i.USB_DevInit)
    USB_DisableGlobalInt                     0x08002b49   Thumb Code    14  stm32f1xx_ll_usb.o(i.USB_DisableGlobalInt)
    USB_EP0_OutStart                         0x08002b57   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_EP0_OutStart)
    USB_EPClearStall                         0x08002b5b   Thumb Code   120  stm32f1xx_ll_usb.o(i.USB_EPClearStall)
    USB_EPSetStall                           0x08002bd3   Thumb Code    50  stm32f1xx_ll_usb.o(i.USB_EPSetStall)
    USB_EPStartXfer                          0x08002c05   Thumb Code  1250  stm32f1xx_ll_usb.o(i.USB_EPStartXfer)
    USB_EnableGlobalInt                      0x080030e7   Thumb Code    18  stm32f1xx_ll_usb.o(i.USB_EnableGlobalInt)
    USB_IO_rest                              0x080030f9   Thumb Code    78  main.o(i.USB_IO_rest)
    USB_LP_CAN1_RX0_IRQHandler               0x08003151   Thumb Code     6  stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler)
    USB_ReadInterrupts                       0x0800315d   Thumb Code     6  stm32f1xx_ll_usb.o(i.USB_ReadInterrupts)
    USB_ReadPMA                              0x08003165   Thumb Code    44  stm32f1xx_ll_usb.o(i.USB_ReadPMA)
    USB_SetCurrentMode                       0x08003191   Thumb Code     4  stm32f1xx_ll_usb.o(i.USB_SetCurrentMode)
    USB_SetDevAddress                        0x08003195   Thumb Code    12  stm32f1xx_ll_usb.o(i.USB_SetDevAddress)
    USB_WritePMA                             0x080031a1   Thumb Code    38  stm32f1xx_ll_usb.o(i.USB_WritePMA)
    UsageFault_Handler                       0x080031c7   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    main                                     0x080031e9   Thumb Code    94  main.o(i.main)
    AHBPrescTable                            0x08003266   Data          16  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x08003278   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003298   Number         0  anon$$obj.o(Region$$Table)
    key_flag                                 0x20000000   Data           1  main.o(.data)
    usb_rx_buffer                            0x20000001   Data          64  main.o(.data)
    usb_rx_flag                              0x20000041   Data           1  main.o(.data)
    FS_Desc                                  0x20000044   Data          28  usbd_desc.o(.data)
    USBD_LangIDDesc                          0x20000060   Data           4  usbd_desc.o(.data)
    USBD_FS_DeviceDesc                       0x20000064   Data          18  usbd_desc.o(.data)
    USBD_StringSerial                        0x20000078   Data          26  usbd_desc.o(.data)
    USBD_CustomHID_fops_FS                   0x200000b0   Data          16  usbd_custom_hid_if.o(.data)
    uwTickFreq                               0x200000c0   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x200000c4   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x200000c8   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x200000cc   Data           4  system_stm32f1xx.o(.data)
    USBD_CUSTOM_HID                          0x200000d4   Data          56  usbd_customhid.o(.data)
    hUsbDeviceFS                             0x200001a8   Data         708  usb_device.o(.bss)
    USBD_StrDesc                             0x2000046c   Data         512  usbd_desc.o(.bss)
    hpcd_USB_FS                              0x200006c4   Data         728  usbd_conf.o(.bss)
    __libspace_start                         0x2000099c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200009fc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003440, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x00003358])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003298, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         2572  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         2735    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000005a   Code   RO         2733    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000182   0x08000182   0x00000002   PAD
    0x08000184   0x08000184   0x0000001c   Code   RO         2737    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a0   0x080001a0   0x00000002   Code   RO         2601    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2608    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2610    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2613    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2615    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2617    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2620    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2622    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2624    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2626    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2628    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2630    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2632    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2634    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2636    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2638    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2640    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2644    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2646    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2648    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000000   Code   RO         2650    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a2   0x080001a2   0x00000002   Code   RO         2651    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a4   0x080001a4   0x00000002   Code   RO         2671    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2684    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2686    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2688    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2691    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2694    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2696    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         2699    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001a6   0x080001a6   0x00000002   Code   RO         2700    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001a8   0x080001a8   0x00000000   Code   RO         2574    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001a8   0x080001a8   0x00000000   Code   RO         2578    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001a8   0x080001a8   0x00000006   Code   RO         2590    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001ae   0x080001ae   0x00000000   Code   RO         2580    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001ae   0x080001ae   0x00000004   Code   RO         2581    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b2   0x080001b2   0x00000000   Code   RO         2583    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b2   0x080001b2   0x00000008   Code   RO         2584    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001ba   0x080001ba   0x00000002   Code   RO         2605    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         2653    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         2654    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c0   0x080001c0   0x00000006   Code   RO         2655    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001c6   0x080001c6   0x00000002   PAD
    0x080001c8   0x080001c8   0x00000040   Code   RO            4    .text               startup_stm32f103xb.o
    0x08000208   0x08000208   0x0000004e   Code   RO         2566    .text               c_w.l(rt_memclr_w.o)
    0x08000256   0x08000256   0x00000056   Code   RO         2568    .text               c_w.l(strncpy.o)
    0x080002ac   0x080002ac   0x00000006   Code   RO         2570    .text               c_w.l(heapauxi.o)
    0x080002b2   0x080002b2   0x00000044   Code   RO         2575    .text               c_w.l(rt_memclr.o)
    0x080002f6   0x080002f6   0x0000004a   Code   RO         2592    .text               c_w.l(sys_stackheap_outer.o)
    0x08000340   0x08000340   0x00000012   Code   RO         2594    .text               c_w.l(exit.o)
    0x08000352   0x08000352   0x00000002   PAD
    0x08000354   0x08000354   0x00000008   Code   RO         2602    .text               c_w.l(libspace.o)
    0x0800035c   0x0800035c   0x0000000c   Code   RO         2663    .text               c_w.l(sys_exit.o)
    0x08000368   0x08000368   0x00000002   Code   RO         2674    .text               c_w.l(use_no_semi.o)
    0x0800036a   0x0800036a   0x00000000   Code   RO         2676    .text               c_w.l(indicate_semi.o)
    0x0800036a   0x0800036a   0x00000002   Code   RO          213    i.BusFault_Handler  stm32f1xx_it.o
    0x0800036c   0x0800036c   0x00000004   Code   RO          446    i.CUSTOM_HID_DeInit_FS  usbd_custom_hid_if.o
    0x08000370   0x08000370   0x00000004   Code   RO          447    i.CUSTOM_HID_Init_FS  usbd_custom_hid_if.o
    0x08000374   0x08000374   0x00000028   Code   RO          448    i.CUSTOM_HID_OutEvent_FS  usbd_custom_hid_if.o
    0x0800039c   0x0800039c   0x00000002   Code   RO          214    i.DebugMon_Handler  stm32f1xx_it.o
    0x0800039e   0x0800039e   0x00000006   Code   RO          215    i.EXTI0_IRQHandler  stm32f1xx_it.o
    0x080003a4   0x080003a4   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x080003a8   0x080003a8   0x0000000c   Code   RO           14    i.HAL_GPIO_EXTI_Callback  main.o
    0x080003b4   0x080003b4   0x00000018   Code   RO         1516    i.HAL_GPIO_EXTI_IRQHandler  stm32f1xx_hal_gpio.o
    0x080003cc   0x080003cc   0x000001e0   Code   RO         1517    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x080005ac   0x080005ac   0x0000000a   Code   RO         1521    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x080005b6   0x080005b6   0x00000002   PAD
    0x080005b8   0x080005b8   0x0000000c   Code   RO         1211    i.HAL_GetTick       stm32f1xx_hal.o
    0x080005c4   0x080005c4   0x00000010   Code   RO         1217    i.HAL_IncTick       stm32f1xx_hal.o
    0x080005d4   0x080005d4   0x00000024   Code   RO         1218    i.HAL_Init          stm32f1xx_hal.o
    0x080005f8   0x080005f8   0x00000040   Code   RO         1219    i.HAL_InitTick      stm32f1xx_hal.o
    0x08000638   0x08000638   0x0000003c   Code   RO          301    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08000674   0x08000674   0x0000001a   Code   RO         1677    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x0800068e   0x0800068e   0x00000002   PAD
    0x08000690   0x08000690   0x00000040   Code   RO         1683    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080006d0   0x080006d0   0x00000024   Code   RO         1684    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x080006f4   0x080006f4   0x0000003c   Code   RO          974    i.HAL_PCDEx_PMAConfig  stm32f1xx_hal_pcd_ex.o
    0x08000730   0x08000730   0x00000002   Code   RO          487    i.HAL_PCDEx_SetConnectionState  usbd_conf.o
    0x08000732   0x08000732   0x00000012   Code   RO          489    i.HAL_PCD_DataInStageCallback  usbd_conf.o
    0x08000744   0x08000744   0x00000014   Code   RO          490    i.HAL_PCD_DataOutStageCallback  usbd_conf.o
    0x08000758   0x08000758   0x0000004a   Code   RO          745    i.HAL_PCD_EP_Close  stm32f1xx_hal_pcd.o
    0x080007a2   0x080007a2   0x0000005a   Code   RO          746    i.HAL_PCD_EP_ClrStall  stm32f1xx_hal_pcd.o
    0x080007fc   0x080007fc   0x000000e6   Code   RO          747    i.HAL_PCD_EP_DB_Receive  stm32f1xx_hal_pcd.o
    0x080008e2   0x080008e2   0x00000338   Code   RO          748    i.HAL_PCD_EP_DB_Transmit  stm32f1xx_hal_pcd.o
    0x08000c1a   0x08000c1a   0x00000058   Code   RO          751    i.HAL_PCD_EP_Open   stm32f1xx_hal_pcd.o
    0x08000c72   0x08000c72   0x0000002e   Code   RO          752    i.HAL_PCD_EP_Receive  stm32f1xx_hal_pcd.o
    0x08000ca0   0x08000ca0   0x00000070   Code   RO          753    i.HAL_PCD_EP_SetStall  stm32f1xx_hal_pcd.o
    0x08000d10   0x08000d10   0x0000002e   Code   RO          754    i.HAL_PCD_EP_Transmit  stm32f1xx_hal_pcd.o
    0x08000d3e   0x08000d3e   0x00000002   PAD
    0x08000d40   0x08000d40   0x00000134   Code   RO          756    i.HAL_PCD_IRQHandler  stm32f1xx_hal_pcd.o
    0x08000e74   0x08000e74   0x000000ba   Code   RO          759    i.HAL_PCD_Init      stm32f1xx_hal_pcd.o
    0x08000f2e   0x08000f2e   0x00000002   PAD
    0x08000f30   0x08000f30   0x0000003c   Code   RO          495    i.HAL_PCD_MspInit   usbd_conf.o
    0x08000f6c   0x08000f6c   0x00000026   Code   RO          496    i.HAL_PCD_ResetCallback  usbd_conf.o
    0x08000f92   0x08000f92   0x00000008   Code   RO          497    i.HAL_PCD_ResumeCallback  usbd_conf.o
    0x08000f9a   0x08000f9a   0x00000008   Code   RO          498    i.HAL_PCD_SOFCallback  usbd_conf.o
    0x08000fa2   0x08000fa2   0x00000026   Code   RO          765    i.HAL_PCD_SetAddress  stm32f1xx_hal_pcd.o
    0x08000fc8   0x08000fc8   0x0000000e   Code   RO          499    i.HAL_PCD_SetupStageCallback  usbd_conf.o
    0x08000fd6   0x08000fd6   0x00000032   Code   RO          767    i.HAL_PCD_Start     stm32f1xx_hal_pcd.o
    0x08001008   0x08001008   0x00000024   Code   RO          500    i.HAL_PCD_SuspendCallback  usbd_conf.o
    0x0800102c   0x0800102c   0x000000ec   Code   RO         1479    i.HAL_RCCEx_PeriphCLKConfig  stm32f1xx_hal_rcc_ex.o
    0x08001118   0x08001118   0x0000012c   Code   RO         1375    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08001244   0x08001244   0x0000004c   Code   RO         1384    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08001290   0x08001290   0x00000320   Code   RO         1387    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x080015b0   0x080015b0   0x00000028   Code   RO         1688    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x080015d8   0x080015d8   0x00000002   Code   RO          216    i.HardFault_Handler  stm32f1xx_it.o
    0x080015da   0x080015da   0x00000002   PAD
    0x080015dc   0x080015dc   0x00000038   Code   RO          360    i.IntToUnicode      usbd_desc.o
    0x08001614   0x08001614   0x000000bc   Code   RO          189    i.MX_GPIO_Init      gpio.o
    0x080016d0   0x080016d0   0x00000054   Code   RO          325    i.MX_USB_DEVICE_Init  usb_device.o
    0x08001724   0x08001724   0x00000002   Code   RO          217    i.MemManage_Handler  stm32f1xx_it.o
    0x08001726   0x08001726   0x00000002   Code   RO          218    i.NMI_Handler       stm32f1xx_it.o
    0x08001728   0x08001728   0x000003a6   Code   RO          771    i.PCD_EP_ISR_Handler  stm32f1xx_hal_pcd.o
    0x08001ace   0x08001ace   0x00000002   Code   RO          219    i.PendSV_Handler    stm32f1xx_it.o
    0x08001ad0   0x08001ad0   0x00000002   Code   RO          220    i.SVC_Handler       stm32f1xx_it.o
    0x08001ad2   0x08001ad2   0x00000004   Code   RO          221    i.SysTick_Handler   stm32f1xx_it.o
    0x08001ad6   0x08001ad6   0x00000076   Code   RO           15    i.SystemClock_Config  main.o
    0x08001b4c   0x08001b4c   0x00000002   Code   RO         2178    i.SystemInit        system_stm32f1xx.o
    0x08001b4e   0x08001b4e   0x0000000e   Code   RO         2478    i.USBD_CUSTOM_HID_DataIn  usbd_customhid.o
    0x08001b5c   0x08001b5c   0x00000024   Code   RO         2479    i.USBD_CUSTOM_HID_DataOut  usbd_customhid.o
    0x08001b80   0x08001b80   0x00000038   Code   RO         2480    i.USBD_CUSTOM_HID_DeInit  usbd_customhid.o
    0x08001bb8   0x08001bb8   0x00000022   Code   RO         2481    i.USBD_CUSTOM_HID_EP0_RxReady  usbd_customhid.o
    0x08001bda   0x08001bda   0x00000002   PAD
    0x08001bdc   0x08001bdc   0x0000000c   Code   RO         2482    i.USBD_CUSTOM_HID_GetDeviceQualifierDesc  usbd_customhid.o
    0x08001be8   0x08001be8   0x0000000c   Code   RO         2483    i.USBD_CUSTOM_HID_GetFSCfgDesc  usbd_customhid.o
    0x08001bf4   0x08001bf4   0x0000000c   Code   RO         2484    i.USBD_CUSTOM_HID_GetHSCfgDesc  usbd_customhid.o
    0x08001c00   0x08001c00   0x0000000c   Code   RO         2485    i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc  usbd_customhid.o
    0x08001c0c   0x08001c0c   0x00000054   Code   RO         2486    i.USBD_CUSTOM_HID_Init  usbd_customhid.o
    0x08001c60   0x08001c60   0x00000010   Code   RO         2487    i.USBD_CUSTOM_HID_RegisterInterface  usbd_customhid.o
    0x08001c70   0x08001c70   0x0000002c   Code   RO         2488    i.USBD_CUSTOM_HID_SendReport  usbd_customhid.o
    0x08001c9c   0x08001c9c   0x000000e0   Code   RO         2489    i.USBD_CUSTOM_HID_Setup  usbd_customhid.o
    0x08001d7c   0x08001d7c   0x0000000e   Code   RO         2215    i.USBD_ClrClassConfig  usbd_core.o
    0x08001d8a   0x08001d8a   0x00000010   Code   RO         2418    i.USBD_CtlContinueRx  usbd_ioreq.o
    0x08001d9a   0x08001d9a   0x00000010   Code   RO         2419    i.USBD_CtlContinueSendData  usbd_ioreq.o
    0x08001daa   0x08001daa   0x00000016   Code   RO         2353    i.USBD_CtlError     usbd_ctlreq.o
    0x08001dc0   0x08001dc0   0x0000001e   Code   RO         2420    i.USBD_CtlPrepareRx  usbd_ioreq.o
    0x08001dde   0x08001dde   0x00000016   Code   RO         2421    i.USBD_CtlReceiveStatus  usbd_ioreq.o
    0x08001df4   0x08001df4   0x0000001a   Code   RO         2422    i.USBD_CtlSendData  usbd_ioreq.o
    0x08001e0e   0x08001e0e   0x00000016   Code   RO         2423    i.USBD_CtlSendStatus  usbd_ioreq.o
    0x08001e24   0x08001e24   0x00000028   Code   RO          361    i.USBD_FS_ConfigStrDescriptor  usbd_desc.o
    0x08001e4c   0x08001e4c   0x0000000c   Code   RO          362    i.USBD_FS_DeviceDescriptor  usbd_desc.o
    0x08001e58   0x08001e58   0x0000002c   Code   RO          363    i.USBD_FS_InterfaceStrDescriptor  usbd_desc.o
    0x08001e84   0x08001e84   0x0000000c   Code   RO          364    i.USBD_FS_LangIDStrDescriptor  usbd_desc.o
    0x08001e90   0x08001e90   0x00000020   Code   RO          365    i.USBD_FS_ManufacturerStrDescriptor  usbd_desc.o
    0x08001eb0   0x08001eb0   0x00000034   Code   RO          366    i.USBD_FS_ProductStrDescriptor  usbd_desc.o
    0x08001ee4   0x08001ee4   0x00000038   Code   RO          367    i.USBD_FS_SerialStrDescriptor  usbd_desc.o
    0x08001f1c   0x08001f1c   0x000000e8   Code   RO         2354    i.USBD_GetDescriptor  usbd_ctlreq.o
    0x08002004   0x08002004   0x00000048   Code   RO         2355    i.USBD_GetString    usbd_ctlreq.o
    0x0800204c   0x0800204c   0x00000018   Code   RO          501    i.USBD_Get_USB_Status  usbd_conf.o
    0x08002064   0x08002064   0x0000002a   Code   RO         2217    i.USBD_Init         usbd_core.o
    0x0800208e   0x0800208e   0x00000012   Code   RO          502    i.USBD_LL_ClearStallEP  usbd_conf.o
    0x080020a0   0x080020a0   0x00000012   Code   RO          503    i.USBD_LL_CloseEP   usbd_conf.o
    0x080020b2   0x080020b2   0x000000c6   Code   RO         2218    i.USBD_LL_DataInStage  usbd_core.o
    0x08002178   0x08002178   0x00000082   Code   RO         2219    i.USBD_LL_DataOutStage  usbd_core.o
    0x080021fa   0x080021fa   0x00000002   PAD
    0x080021fc   0x080021fc   0x00000070   Code   RO          508    i.USBD_LL_Init      usbd_conf.o
    0x0800226c   0x0800226c   0x0000001e   Code   RO          509    i.USBD_LL_IsStallEP  usbd_conf.o
    0x0800228a   0x0800228a   0x00000018   Code   RO          510    i.USBD_LL_OpenEP    usbd_conf.o
    0x080022a2   0x080022a2   0x00000012   Code   RO          511    i.USBD_LL_PrepareReceive  usbd_conf.o
    0x080022b4   0x080022b4   0x00000050   Code   RO         2224    i.USBD_LL_Reset     usbd_core.o
    0x08002304   0x08002304   0x00000014   Code   RO         2225    i.USBD_LL_Resume    usbd_core.o
    0x08002318   0x08002318   0x00000018   Code   RO         2226    i.USBD_LL_SOF       usbd_core.o
    0x08002330   0x08002330   0x00000006   Code   RO         2227    i.USBD_LL_SetSpeed  usbd_core.o
    0x08002336   0x08002336   0x00000012   Code   RO          512    i.USBD_LL_SetUSBAddress  usbd_conf.o
    0x08002348   0x08002348   0x0000005a   Code   RO         2228    i.USBD_LL_SetupStage  usbd_core.o
    0x080023a2   0x080023a2   0x00000012   Code   RO          513    i.USBD_LL_StallEP   usbd_conf.o
    0x080023b4   0x080023b4   0x00000012   Code   RO          514    i.USBD_LL_Start     usbd_conf.o
    0x080023c6   0x080023c6   0x00000012   Code   RO         2229    i.USBD_LL_Suspend   usbd_core.o
    0x080023d8   0x080023d8   0x00000012   Code   RO          516    i.USBD_LL_Transmit  usbd_conf.o
    0x080023ea   0x080023ea   0x00000028   Code   RO         2356    i.USBD_ParseSetupRequest  usbd_ctlreq.o
    0x08002412   0x08002412   0x00000010   Code   RO         2230    i.USBD_RegisterClass  usbd_core.o
    0x08002422   0x08002422   0x00000016   Code   RO         2232    i.USBD_SetClassConfig  usbd_core.o
    0x08002438   0x08002438   0x00000084   Code   RO         2357    i.USBD_SetConfig    usbd_ctlreq.o
    0x080024bc   0x080024bc   0x0000000a   Code   RO         2233    i.USBD_Start        usbd_core.o
    0x080024c6   0x080024c6   0x00000112   Code   RO         2358    i.USBD_StdDevReq    usbd_ctlreq.o
    0x080025d8   0x080025d8   0x00000150   Code   RO         2359    i.USBD_StdEPReq     usbd_ctlreq.o
    0x08002728   0x08002728   0x0000004e   Code   RO         2360    i.USBD_StdItfReq    usbd_ctlreq.o
    0x08002776   0x08002776   0x00000002   Code   RO          517    i.USBD_static_free  usbd_conf.o
    0x08002778   0x08002778   0x00000008   Code   RO          518    i.USBD_static_malloc  usbd_conf.o
    0x08002780   0x08002780   0x0000029c   Code   RO         1014    i.USB_ActivateEndpoint  stm32f1xx_ll_usb.o
    0x08002a1c   0x08002a1c   0x00000004   Code   RO         1017    i.USB_CoreInit      stm32f1xx_ll_usb.o
    0x08002a20   0x08002a20   0x0000010e   Code   RO         1019    i.USB_DeactivateEndpoint  stm32f1xx_ll_usb.o
    0x08002b2e   0x08002b2e   0x00000004   Code   RO         1020    i.USB_DevConnect    stm32f1xx_ll_usb.o
    0x08002b32   0x08002b32   0x00000004   Code   RO         1021    i.USB_DevDisconnect  stm32f1xx_ll_usb.o
    0x08002b36   0x08002b36   0x00000012   Code   RO         1022    i.USB_DevInit       stm32f1xx_ll_usb.o
    0x08002b48   0x08002b48   0x0000000e   Code   RO         1023    i.USB_DisableGlobalInt  stm32f1xx_ll_usb.o
    0x08002b56   0x08002b56   0x00000004   Code   RO         1024    i.USB_EP0_OutStart  stm32f1xx_ll_usb.o
    0x08002b5a   0x08002b5a   0x00000078   Code   RO         1025    i.USB_EPClearStall  stm32f1xx_ll_usb.o
    0x08002bd2   0x08002bd2   0x00000032   Code   RO         1026    i.USB_EPSetStall    stm32f1xx_ll_usb.o
    0x08002c04   0x08002c04   0x000004e2   Code   RO         1027    i.USB_EPStartXfer   stm32f1xx_ll_usb.o
    0x080030e6   0x080030e6   0x00000012   Code   RO         1029    i.USB_EnableGlobalInt  stm32f1xx_ll_usb.o
    0x080030f8   0x080030f8   0x00000058   Code   RO           16    i.USB_IO_rest       main.o
    0x08003150   0x08003150   0x0000000c   Code   RO          222    i.USB_LP_CAN1_RX0_IRQHandler  stm32f1xx_it.o
    0x0800315c   0x0800315c   0x00000006   Code   RO         1036    i.USB_ReadInterrupts  stm32f1xx_ll_usb.o
    0x08003162   0x08003162   0x00000002   PAD
    0x08003164   0x08003164   0x0000002c   Code   RO         1037    i.USB_ReadPMA       stm32f1xx_ll_usb.o
    0x08003190   0x08003190   0x00000004   Code   RO         1038    i.USB_SetCurrentMode  stm32f1xx_ll_usb.o
    0x08003194   0x08003194   0x0000000c   Code   RO         1039    i.USB_SetDevAddress  stm32f1xx_ll_usb.o
    0x080031a0   0x080031a0   0x00000026   Code   RO         1041    i.USB_WritePMA      stm32f1xx_ll_usb.o
    0x080031c6   0x080031c6   0x00000002   Code   RO          223    i.UsageFault_Handler  stm32f1xx_it.o
    0x080031c8   0x080031c8   0x00000020   Code   RO         1690    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080031e8   0x080031e8   0x0000006c   Code   RO           17    i.main              main.o
    0x08003254   0x08003254   0x00000012   Data   RO         1388    .constdata          stm32f1xx_hal_rcc.o
    0x08003266   0x08003266   0x00000010   Data   RO         2179    .constdata          system_stm32f1xx.o
    0x08003276   0x08003276   0x00000002   PAD
    0x08003278   0x08003278   0x00000020   Data   RO         2731    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003298, Size: 0x00001000, Max: 0x00005000, ABSOLUTE, COMPRESSED[0x000000c0])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000041   Data   RW           19    .data               main.o
    0x20000041   COMPRESSED   0x00000001   Data   RW           20    .data               main.o
    0x20000042   COMPRESSED   0x00000002   PAD
    0x20000044   COMPRESSED   0x0000001c   Data   RW          369    .data               usbd_desc.o
    0x20000060   COMPRESSED   0x00000032   Data   RW          370    .data               usbd_desc.o
    0x20000092   COMPRESSED   0x00000002   PAD
    0x20000094   COMPRESSED   0x00000019   Data   RW          449    .data               usbd_custom_hid_if.o
    0x200000ad   COMPRESSED   0x00000003   PAD
    0x200000b0   COMPRESSED   0x00000010   Data   RW          450    .data               usbd_custom_hid_if.o
    0x200000c0   COMPRESSED   0x0000000c   Data   RW         1225    .data               stm32f1xx_hal.o
    0x200000cc   COMPRESSED   0x00000004   Data   RW         2181    .data               system_stm32f1xx.o
    0x200000d0   COMPRESSED   0x00000001   Data   RW         2361    .data               usbd_ctlreq.o
    0x200000d1   COMPRESSED   0x00000003   PAD
    0x200000d4   COMPRESSED   0x00000038   Data   RW         2490    .data               usbd_customhid.o
    0x2000010c   COMPRESSED   0x0000009a   Data   RW         2491    .data               usbd_customhid.o
    0x200001a6   COMPRESSED   0x00000002   PAD
    0x200001a8        -       0x000002c4   Zero   RW          326    .bss                usb_device.o
    0x2000046c        -       0x00000200   Zero   RW          368    .bss                usbd_desc.o
    0x2000066c        -       0x00000330   Zero   RW          519    .bss                usbd_conf.o
    0x2000099c        -       0x00000060   Zero   RW         2603    .bss                c_w.l(libspace.o)
    0x200009fc   COMPRESSED   0x00000004   PAD
    0x20000a00        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f103xb.o
    0x20000c00        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       188         18          0          0          0        943   gpio.o
       330         28          0         66          0     403401   main.o
        64         26        236          0       1536        808   startup_stm32f103xb.o
       128         24          0         12          0       5277   stm32f1xx_hal.o
       198         14          0          0          0      28947   stm32f1xx_hal_cortex.o
       514         40          0          0          0       3471   stm32f1xx_hal_gpio.o
        60          8          0          0          0        870   stm32f1xx_hal_msp.o
      3026          0          0          0          0      13605   stm32f1xx_hal_pcd.o
        60          0          0          0          0       1166   stm32f1xx_hal_pcd_ex.o
      1176         60         18          0          0       3974   stm32f1xx_hal_rcc.o
       236         12          0          0          0       1380   stm32f1xx_hal_rcc_ex.o
        38          6          0          0          0       4722   stm32f1xx_it.o
      2528          0          0          0          0      16067   stm32f1xx_ll_usb.o
         2          0         16          4          0       1107   system_stm32f1xx.o
        84         18          0          0        708        788   usb_device.o
       530         28          0          0        816      13601   usbd_conf.o
       670          0          0          0          0       9419   usbd_core.o
      1186         28          0          1          0       7888   usbd_ctlreq.o
        48         14          0         41          0       2238   usbd_custom_hid_if.o
       556         32          0        210          0      10735   usbd_customhid.o
       304        120          0         78        512       5913   usbd_desc.o
       132          0          0          0          0       4304   usbd_ioreq.o

    ----------------------------------------------------------------------
     12074        <USER>        <GROUP>        424       3572     540624   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          2         12          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        86          0          0          0          0         76   strncpy.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       574         <USER>          <GROUP>          0        100        808   Library Totals
         6          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       568         16          0          0         96        808   c_w.l

    ----------------------------------------------------------------------
       574         <USER>          <GROUP>          0        100        808   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12648        492        304        424       3672     534200   Grand Totals
     12648        492        304        192       3672     534200   ELF Image Totals (compressed)
     12648        492        304        192          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                12952 (  12.65kB)
    Total RW  Size (RW Data + ZI Data)              4096 (   4.00kB)
    Total ROM Size (Code + RO Data + RW Data)      13144 (  12.84kB)

==============================================================================

