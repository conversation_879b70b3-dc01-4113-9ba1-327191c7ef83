<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [USB_HID\USB_HID.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image USB_HID\USB_HID.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Aug 01 12:09:48 2025
<BR><P>
<H3>Maximum Stack Usage =        192 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
USB_LP_CAN1_RX0_IRQHandler &rArr; HAL_PCD_IRQHandler &rArr; PCD_EP_ISR_Handler &rArr; HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[62]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[33]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[33]">ADC1_2_IRQHandler</a><BR>
 <LI><a href="#[1b]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1b]">BusFault_Handler</a><BR>
 <LI><a href="#[19]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[19]">HardFault_Handler</a><BR>
 <LI><a href="#[1a]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1a]">MemManage_Handler</a><BR>
 <LI><a href="#[18]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[18]">NMI_Handler</a><BR>
 <LI><a href="#[1c]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[33]">ADC1_2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1b]">BusFault_Handler</a> from stm32f1xx_it.o(i.BusFault_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[36]">CAN1_RX1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[37]">CAN1_SCE_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[b]">CUSTOM_HID_DeInit_FS</a> from usbd_custom_hid_if.o(i.CUSTOM_HID_DeInit_FS) referenced 2 times from usbd_custom_hid_if.o(.data)
 <LI><a href="#[a]">CUSTOM_HID_Init_FS</a> from usbd_custom_hid_if.o(i.CUSTOM_HID_Init_FS) referenced 2 times from usbd_custom_hid_if.o(.data)
 <LI><a href="#[c]">CUSTOM_HID_OutEvent_FS</a> from usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS) referenced 2 times from usbd_custom_hid_if.o(.data)
 <LI><a href="#[2c]">DMA1_Channel1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2d]">DMA1_Channel2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2e]">DMA1_Channel3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2f]">DMA1_Channel4_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[30]">DMA1_Channel5_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[31]">DMA1_Channel6_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[32]">DMA1_Channel7_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1e]">DebugMon_Handler</a> from stm32f1xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[27]">EXTI0_IRQHandler</a> from stm32f1xx_it.o(i.EXTI0_IRQHandler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[49]">EXTI15_10_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[28]">EXTI1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[29]">EXTI2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2a]">EXTI3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2b]">EXTI4_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[38]">EXTI9_5_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[25]">FLASH_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[19]">HardFault_Handler</a> from stm32f1xx_it.o(i.HardFault_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[41]">I2C1_ER_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[40]">I2C1_EV_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[43]">I2C2_ER_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[42]">I2C2_EV_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1a]">MemManage_Handler</a> from stm32f1xx_it.o(i.MemManage_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[18]">NMI_Handler</a> from stm32f1xx_it.o(i.NMI_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[22]">PVD_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1f]">PendSV_Handler</a> from stm32f1xx_it.o(i.PendSV_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[26]">RCC_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[4a]">RTC_Alarm_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[24]">RTC_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[17]">Reset_Handler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[44]">SPI1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[45]">SPI2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1d]">SVC_Handler</a> from stm32f1xx_it.o(i.SVC_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[20]">SysTick_Handler</a> from stm32f1xx_it.o(i.SysTick_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[4c]">SystemInit</a> from system_stm32f1xx.o(i.SystemInit) referenced from startup_stm32f103xb.o(.text)
 <LI><a href="#[23]">TAMPER_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[39]">TIM1_BRK_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[3c]">TIM1_CC_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[3b]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[3a]">TIM1_UP_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[3d]">TIM2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[3e]">TIM3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[3f]">TIM4_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[46]">USART1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[47]">USART2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[48]">USART3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[11]">USBD_CUSTOM_HID_DataIn</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_DataIn) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[12]">USBD_CUSTOM_HID_DataOut</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[e]">USBD_CUSTOM_HID_DeInit</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[10]">USBD_CUSTOM_HID_EP0_RxReady</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_EP0_RxReady) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[16]">USBD_CUSTOM_HID_GetDeviceQualifierDesc</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[14]">USBD_CUSTOM_HID_GetFSCfgDesc</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[13]">USBD_CUSTOM_HID_GetHSCfgDesc</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[15]">USBD_CUSTOM_HID_GetOtherSpeedCfgDesc</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[d]">USBD_CUSTOM_HID_Init</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_Init) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[f]">USBD_CUSTOM_HID_Setup</a> from usbd_customhid.o(i.USBD_CUSTOM_HID_Setup) referenced 2 times from usbd_customhid.o(.data)
 <LI><a href="#[8]">USBD_FS_ConfigStrDescriptor</a> from usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[3]">USBD_FS_DeviceDescriptor</a> from usbd_desc.o(i.USBD_FS_DeviceDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[9]">USBD_FS_InterfaceStrDescriptor</a> from usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[4]">USBD_FS_LangIDStrDescriptor</a> from usbd_desc.o(i.USBD_FS_LangIDStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[5]">USBD_FS_ManufacturerStrDescriptor</a> from usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[6]">USBD_FS_ProductStrDescriptor</a> from usbd_desc.o(i.USBD_FS_ProductStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[7]">USBD_FS_SerialStrDescriptor</a> from usbd_desc.o(i.USBD_FS_SerialStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[4b]">USBWakeUp_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[34]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[35]">USB_LP_CAN1_RX0_IRQHandler</a> from stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1c]">UsageFault_Handler</a> from stm32f1xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[21]">WWDG_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[4e]">__main</a> from __main.o(!!!main) referenced from startup_stm32f103xb.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[4e]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[4f]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[51]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[d7]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[d8]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[d9]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[da]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[db]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[55]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[dc]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[dd]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[de]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[df]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[e0]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[e1]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[e2]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[e3]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[e4]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[e5]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[e6]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[e7]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[e8]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[e9]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[ea]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[eb]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[ec]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[ed]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[ee]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[ef]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[f0]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[5a]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[f1]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[f2]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[f3]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[f4]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[f5]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[f6]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[f7]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[f8]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[50]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[f9]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[52]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[54]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[fa]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[56]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 152 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[fb]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[63]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[59]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[fc]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[5b]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[17]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f103xb.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[b3]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[fd]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[fe]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[60]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[5d]"></a>strncpy</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CUSTOM_HID_OutEvent_FS
</UL>

<P><STRONG><a name="[ff]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[100]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[101]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[5e]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>

<P><STRONG><a name="[5f]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[102]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[53]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[58]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[103]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[61]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[104]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[5c]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[105]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[106]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1b]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[107]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[1e]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI0_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[97]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResetCallback
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
</UL>

<P><STRONG><a name="[65]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f1xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_WakeupDevice
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_MonitorStatus
</UL>

<P><STRONG><a name="[67]"></a>HAL_GPIO_EXTI_Callback</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, main.o(i.HAL_GPIO_EXTI_Callback))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[64]"></a>HAL_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler
</UL>

<P><STRONG><a name="[ab]"></a>HAL_GPIO_Init</STRONG> (Thumb, 446 bytes, Stack size 40 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_IO_rest
</UL>

<P><STRONG><a name="[aa]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_MonitorStatus
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_IO_rest
</UL>

<P><STRONG><a name="[66]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_MonitorStatus
</UL>

<P><STRONG><a name="[b1]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f1xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[68]"></a>HAL_Init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f1xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6a]"></a>HAL_InitTick</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f1xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[6b]"></a>HAL_MspInit</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f1xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[96]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[6d]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[69]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[c9]"></a>HAL_PCDEx_PMAConfig</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f1xx_hal_pcd_ex.o(i.HAL_PCDEx_PMAConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCDEx_PMAConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>

<P><STRONG><a name="[a1]"></a>HAL_PCDEx_SetConnectionState</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCDEx_SetConnectionState))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Stop
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
</UL>

<P><STRONG><a name="[6f]"></a>HAL_PCD_ActivateRemoteWakeup</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_ActivateRemoteWakeup))
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ActivateRemoteWakeup
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_WakeupDevice
</UL>

<P><STRONG><a name="[71]"></a>HAL_PCD_DataInStageCallback</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_DataInStageCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_PCD_DataInStageCallback &rArr; USBD_LL_DataInStage &rArr; USBD_CtlReceiveStatus &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_ISR_Handler
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_DB_Transmit
</UL>

<P><STRONG><a name="[73]"></a>HAL_PCD_DataOutStageCallback</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_DataOutStageCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_PCD_DataOutStageCallback &rArr; USBD_LL_DataOutStage &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataOutStage
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_ISR_Handler
</UL>

<P><STRONG><a name="[75]"></a>HAL_PCD_DeActivateRemoteWakeup</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_DeActivateRemoteWakeup))
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeActivateRemoteWakeup
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_WakeupDevice
</UL>

<P><STRONG><a name="[77]"></a>HAL_PCD_EP_Close</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Close))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_PCD_EP_Close &rArr; USB_DeactivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeactivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_CloseEP
</UL>

<P><STRONG><a name="[79]"></a>HAL_PCD_EP_ClrStall</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_PCD_EP_ClrStall &rArr; USB_EPClearStall
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPClearStall
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_ClearStallEP
</UL>

<P><STRONG><a name="[7f]"></a>HAL_PCD_EP_Open</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Open))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ActivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
</UL>

<P><STRONG><a name="[81]"></a>HAL_PCD_EP_Receive</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPStartXfer
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>

<P><STRONG><a name="[83]"></a>HAL_PCD_EP_SetStall</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_SetStall))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PCD_EP_SetStall &rArr; USB_EPSetStall
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPSetStall
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EP0_OutStart
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
</UL>

<P><STRONG><a name="[86]"></a>HAL_PCD_EP_Transmit</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPStartXfer
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>

<P><STRONG><a name="[87]"></a>HAL_PCD_IRQHandler</STRONG> (Thumb, 308 bytes, Stack size 24 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = HAL_PCD_IRQHandler &rArr; PCD_EP_ISR_Handler &rArr; HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetAddress
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SuspendCallback
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SOFCallback
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResumeCallback
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResetCallback
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ReadInterrupts
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_ISR_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_LP_CAN1_RX0_IRQHandler
</UL>

<P><STRONG><a name="[8f]"></a>HAL_PCD_Init</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_SetCurrentMode
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DisableGlobalInt
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevInit
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevDisconnect
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_CoreInit
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>

<P><STRONG><a name="[90]"></a>HAL_PCD_MspInit</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, usbd_conf.o(i.HAL_PCD_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PCD_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[8a]"></a>HAL_PCD_ResetCallback</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, usbd_conf.o(i.HAL_PCD_ResetCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_PCD_ResetCallback &rArr; USBD_LL_Reset &rArr; USBD_LL_OpenEP &rArr; HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetSpeed
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Reset
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[8c]"></a>HAL_PCD_ResumeCallback</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_ResumeCallback))
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Resume
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[8e]"></a>HAL_PCD_SOFCallback</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_SOFCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCD_SOFCallback &rArr; USBD_LL_SOF
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SOF
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[8b]"></a>HAL_PCD_SetAddress</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_SetAddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCD_SetAddress
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_SetDevAddress
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetUSBAddress
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[9d]"></a>HAL_PCD_SetupStageCallback</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_SetupStageCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_ISR_Handler
</UL>

<P><STRONG><a name="[9f]"></a>HAL_PCD_Start</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCD_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCDEx_SetConnectionState
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EnableGlobalInt
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevConnect
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Start
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_MonitorStatus
</UL>

<P><STRONG><a name="[a3]"></a>HAL_PCD_Stop</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCD_Stop
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCDEx_SetConnectionState
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DisableGlobalInt
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevDisconnect
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_MonitorStatus
</UL>

<P><STRONG><a name="[8d]"></a>HAL_PCD_SuspendCallback</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usbd_conf.o(i.HAL_PCD_SuspendCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCD_SuspendCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Suspend
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[a5]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[a6]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 280 bytes, Stack size 32 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[a7]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[a8]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 778 bytes, Stack size 40 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[6c]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[19]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[a9]"></a>MX_GPIO_Init</STRONG> (Thumb, 170 bytes, Stack size 48 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ac]"></a>MX_USB_DEVICE_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, usb_device.o(i.MX_USB_DEVICE_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = MX_USB_DEVICE_Init &rArr; USBD_Init &rArr; USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Start
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_RegisterClass
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_RegisterInterface
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1a]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[b2]"></a>SystemClock_Config</STRONG> (Thumb, 118 bytes, Stack size 88 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4c]"></a>SystemInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, system_stm32f1xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(.text)
</UL>
<P><STRONG><a name="[af]"></a>USBD_CUSTOM_HID_RegisterInterface</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_RegisterInterface))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
</UL>

<P><STRONG><a name="[b9]"></a>USBD_CUSTOM_HID_SendReport</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_SendReport))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CUSTOM_HID_SendReport &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d1]"></a>USBD_ClrClassConfig</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_ClrClassConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_ClrClassConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
</UL>

<P><STRONG><a name="[be]"></a>USBD_CtlContinueRx</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlContinueRx))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlContinueRx &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataOutStage
</UL>

<P><STRONG><a name="[bf]"></a>USBD_CtlContinueSendData</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlContinueSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlContinueSendData &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
</UL>

<P><STRONG><a name="[bd]"></a>USBD_CtlError</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, usbd_ctlreq.o(i.USBD_CtlError))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = USBD_CtlError &rArr; USBD_LL_StallEP &rArr; HAL_PCD_EP_SetStall &rArr; USB_EPSetStall
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetDescriptor
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdItfReq
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Setup
</UL>

<P><STRONG><a name="[bb]"></a>USBD_CtlPrepareRx</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlPrepareRx))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlPrepareRx &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Setup
</UL>

<P><STRONG><a name="[c1]"></a>USBD_CtlReceiveStatus</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlReceiveStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlReceiveStatus &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
</UL>

<P><STRONG><a name="[bc]"></a>USBD_CtlSendData</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlSendData &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetDescriptor
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Setup
</UL>

<P><STRONG><a name="[c2]"></a>USBD_CtlSendStatus</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlSendStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataOutStage
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetDescriptor
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdItfReq
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[8]"></a>USBD_FS_ConfigStrDescriptor</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_ConfigStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_FS_ConfigStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[3]"></a>USBD_FS_DeviceDescriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_desc.o(i.USBD_FS_DeviceDescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[9]"></a>USBD_FS_InterfaceStrDescriptor</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_FS_InterfaceStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[4]"></a>USBD_FS_LangIDStrDescriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_desc.o(i.USBD_FS_LangIDStrDescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[5]"></a>USBD_FS_ManufacturerStrDescriptor</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_FS_ManufacturerStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[6]"></a>USBD_FS_ProductStrDescriptor</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_ProductStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_FS_ProductStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[7]"></a>USBD_FS_SerialStrDescriptor</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_SerialStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = USBD_FS_SerialStrDescriptor &rArr; IntToUnicode
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntToUnicode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[c3]"></a>USBD_GetString</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, usbd_ctlreq.o(i.USBD_GetString))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBD_GetString
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_ProductStrDescriptor
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_ManufacturerStrDescriptor
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_InterfaceStrDescriptor
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_ConfigStrDescriptor
</UL>

<P><STRONG><a name="[ad]"></a>USBD_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_Init &rArr; USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
</UL>

<P><STRONG><a name="[c7]"></a>USBD_LL_ClearStallEP</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_ClearStallEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = USBD_LL_ClearStallEP &rArr; HAL_PCD_EP_ClrStall &rArr; USB_EPClearStall
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_ClrStall
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
</UL>

<P><STRONG><a name="[b5]"></a>USBD_LL_CloseEP</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_CloseEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = USBD_LL_CloseEP &rArr; HAL_PCD_EP_Close &rArr; USB_DeactivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Close
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_DeInit
</UL>

<P><STRONG><a name="[72]"></a>USBD_LL_DataInStage</STRONG> (Thumb, 198 bytes, Stack size 24 bytes, usbd_core.o(i.USBD_LL_DataInStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = USBD_LL_DataInStage &rArr; USBD_CtlReceiveStatus &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlReceiveStatus
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlContinueSendData
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DataInStageCallback
</UL>

<P><STRONG><a name="[74]"></a>USBD_LL_DataOutStage</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, usbd_core.o(i.USBD_LL_DataOutStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_LL_DataOutStage &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlContinueRx
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DataOutStageCallback
</UL>

<P><STRONG><a name="[c6]"></a>USBD_LL_Init</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCDEx_PMAConfig
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Init
</UL>

<P><STRONG><a name="[d3]"></a>USBD_LL_IsStallEP</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, usbd_conf.o(i.USBD_LL_IsStallEP))
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
</UL>

<P><STRONG><a name="[b7]"></a>USBD_LL_OpenEP</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_OpenEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_LL_OpenEP &rArr; HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Open
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Reset
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Init
</UL>

<P><STRONG><a name="[b4]"></a>USBD_LL_PrepareReceive</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_PrepareReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Receive
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlReceiveStatus
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlContinueRx
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Init
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_DataOut
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlPrepareRx
</UL>

<P><STRONG><a name="[99]"></a>USBD_LL_Reset</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, usbd_core.o(i.USBD_LL_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_LL_Reset &rArr; USBD_LL_OpenEP &rArr; HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResetCallback
</UL>

<P><STRONG><a name="[9a]"></a>USBD_LL_Resume</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_LL_Resume))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResumeCallback
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_WakeupDevice
</UL>

<P><STRONG><a name="[9b]"></a>USBD_LL_SOF</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_LL_SOF))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_LL_SOF
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SOFCallback
</UL>

<P><STRONG><a name="[98]"></a>USBD_LL_SetSpeed</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_LL_SetSpeed))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResetCallback
</UL>

<P><STRONG><a name="[ca]"></a>USBD_LL_SetUSBAddress</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_SetUSBAddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBD_LL_SetUSBAddress &rArr; HAL_PCD_SetAddress
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetAddress
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[9e]"></a>USBD_LL_SetupStage</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, usbd_core.o(i.USBD_LL_SetupStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdItfReq
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_ParseSetupRequest
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetupStageCallback
</UL>

<P><STRONG><a name="[c0]"></a>USBD_LL_StallEP</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_StallEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USBD_LL_StallEP &rArr; HAL_PCD_EP_SetStall &rArr; USB_EPSetStall
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_SetStall
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataOutStage
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>

<P><STRONG><a name="[cf]"></a>USBD_LL_Start</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBD_LL_Start &rArr; HAL_PCD_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Start
</UL>

<P><STRONG><a name="[a4]"></a>USBD_LL_Suspend</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_LL_Suspend))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SuspendCallback
</UL>

<P><STRONG><a name="[ba]"></a>USBD_LL_Transmit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Transmit
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_SendReport
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlContinueSendData
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
</UL>

<P><STRONG><a name="[cb]"></a>USBD_ParseSetupRequest</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, usbd_ctlreq.o(i.USBD_ParseSetupRequest))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>

<P><STRONG><a name="[ae]"></a>USBD_RegisterClass</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_RegisterClass))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
</UL>

<P><STRONG><a name="[d2]"></a>USBD_SetClassConfig</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_SetClassConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_SetClassConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
</UL>

<P><STRONG><a name="[b0]"></a>USBD_Start</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_Start &rArr; USBD_LL_Start &rArr; HAL_PCD_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
</UL>

<P><STRONG><a name="[cc]"></a>USBD_StdDevReq</STRONG> (Thumb, 274 bytes, Stack size 24 bytes, usbd_ctlreq.o(i.USBD_StdDevReq))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetUSBAddress
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetDescriptor
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>

<P><STRONG><a name="[ce]"></a>USBD_StdEPReq</STRONG> (Thumb, 336 bytes, Stack size 24 bytes, usbd_ctlreq.o(i.USBD_StdEPReq))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = USBD_StdEPReq &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_IsStallEP
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_ClearStallEP
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>

<P><STRONG><a name="[cd]"></a>USBD_StdItfReq</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, usbd_ctlreq.o(i.USBD_StdItfReq))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_StdItfReq &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>

<P><STRONG><a name="[b6]"></a>USBD_static_free</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usbd_conf.o(i.USBD_static_free))
<BR><BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_DeInit
</UL>

<P><STRONG><a name="[b8]"></a>USBD_static_malloc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_conf.o(i.USBD_static_malloc))
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Init
</UL>

<P><STRONG><a name="[80]"></a>USB_ActivateEndpoint</STRONG> (Thumb, 668 bytes, Stack size 32 bytes, stm32f1xx_ll_usb.o(i.USB_ActivateEndpoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USB_ActivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Open
</UL>

<P><STRONG><a name="[70]"></a>USB_ActivateRemoteWakeup</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_ActivateRemoteWakeup))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ActivateRemoteWakeup
</UL>

<P><STRONG><a name="[92]"></a>USB_CoreInit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_CoreInit))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[76]"></a>USB_DeActivateRemoteWakeup</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_DeActivateRemoteWakeup))
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DeActivateRemoteWakeup
</UL>

<P><STRONG><a name="[78]"></a>USB_DeactivateEndpoint</STRONG> (Thumb, 270 bytes, Stack size 28 bytes, stm32f1xx_ll_usb.o(i.USB_DeactivateEndpoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = USB_DeactivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Close
</UL>

<P><STRONG><a name="[a2]"></a>USB_DevConnect</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_DevConnect))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
</UL>

<P><STRONG><a name="[95]"></a>USB_DevDisconnect</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_DevDisconnect))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Stop
</UL>

<P><STRONG><a name="[94]"></a>USB_DevInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_DevInit))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[91]"></a>USB_DisableGlobalInt</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_DisableGlobalInt))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Stop
</UL>

<P><STRONG><a name="[85]"></a>USB_EP0_OutStart</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_EP0_OutStart))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_SetStall
</UL>

<P><STRONG><a name="[7a]"></a>USB_EPClearStall</STRONG> (Thumb, 120 bytes, Stack size 12 bytes, stm32f1xx_ll_usb.o(i.USB_EPClearStall))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = USB_EPClearStall
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_ClrStall
</UL>

<P><STRONG><a name="[84]"></a>USB_EPSetStall</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, stm32f1xx_ll_usb.o(i.USB_EPSetStall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_EPSetStall
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_SetStall
</UL>

<P><STRONG><a name="[82]"></a>USB_EPStartXfer</STRONG> (Thumb, 1250 bytes, Stack size 32 bytes, stm32f1xx_ll_usb.o(i.USB_EPStartXfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_WritePMA
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Transmit
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Receive
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_ISR_Handler
</UL>

<P><STRONG><a name="[a0]"></a>USB_EnableGlobalInt</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_EnableGlobalInt))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
</UL>

<P><STRONG><a name="[d4]"></a>USB_IO_rest</STRONG> (Thumb, 78 bytes, Stack size 40 bytes, main.o(i.USB_IO_rest))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USB_IO_rest &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[35]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = USB_LP_CAN1_RX0_IRQHandler &rArr; HAL_PCD_IRQHandler &rArr; PCD_EP_ISR_Handler &rArr; HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[d5]"></a>USB_MonitorStatus</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, main.o(i.USB_MonitorStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USB_MonitorStatus &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Stop
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[88]"></a>USB_ReadInterrupts</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_ReadInterrupts))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[7c]"></a>USB_ReadPMA</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f1xx_ll_usb.o(i.USB_ReadPMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_ReadPMA
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_ISR_Handler
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_DB_Receive
</UL>

<P><STRONG><a name="[93]"></a>USB_SetCurrentMode</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_SetCurrentMode))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[9c]"></a>USB_SetDevAddress</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f1xx_ll_usb.o(i.USB_SetDevAddress))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetAddress
</UL>

<P><STRONG><a name="[d6]"></a>USB_WakeupDevice</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, main.o(i.USB_WakeupDevice))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USB_WakeupDevice &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Resume
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DeActivateRemoteWakeup
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ActivateRemoteWakeup
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7e]"></a>USB_WritePMA</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f1xx_ll_usb.o(i.USB_WritePMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_WritePMA
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPStartXfer
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_DB_Transmit
</UL>

<P><STRONG><a name="[1c]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>main</STRONG> (Thumb, 186 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_SendReport
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_WakeupDevice
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_MonitorStatus
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_IO_rest
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[c4]"></a>IntToUnicode</STRONG> (Thumb, 56 bytes, Stack size 20 bytes, usbd_desc.o(i.IntToUnicode))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = IntToUnicode
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_SerialStrDescriptor
</UL>

<P><STRONG><a name="[b]"></a>CUSTOM_HID_DeInit_FS</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_custom_hid_if.o(i.CUSTOM_HID_DeInit_FS))
<BR>[Address Reference Count : 1]<UL><LI> usbd_custom_hid_if.o(.data)
</UL>
<P><STRONG><a name="[a]"></a>CUSTOM_HID_Init_FS</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_custom_hid_if.o(i.CUSTOM_HID_Init_FS))
<BR>[Address Reference Count : 1]<UL><LI> usbd_custom_hid_if.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>CUSTOM_HID_OutEvent_FS</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, usbd_custom_hid_if.o(i.CUSTOM_HID_OutEvent_FS))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CUSTOM_HID_OutEvent_FS &rArr; strncpy
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_custom_hid_if.o(.data)
</UL>
<P><STRONG><a name="[c8]"></a>USBD_Get_USB_Status</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, usbd_conf.o(i.USBD_Get_USB_Status))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Start
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetUSBAddress
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_CloseEP
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_ClearStallEP
</UL>

<P><STRONG><a name="[7b]"></a>HAL_PCD_EP_DB_Receive</STRONG> (Thumb, 230 bytes, Stack size 40 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_PCD_EP_DB_Receive &rArr; USB_ReadPMA
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ReadPMA
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_ISR_Handler
</UL>

<P><STRONG><a name="[7d]"></a>HAL_PCD_EP_DB_Transmit</STRONG> (Thumb, 824 bytes, Stack size 40 bytes, stm32f1xx_hal_pcd.o(i.HAL_PCD_EP_DB_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_PCD_EP_DB_Transmit &rArr; HAL_PCD_DataInStageCallback &rArr; USBD_LL_DataInStage &rArr; USBD_CtlReceiveStatus &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DataInStageCallback
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_WritePMA
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_ISR_Handler
</UL>

<P><STRONG><a name="[89]"></a>PCD_EP_ISR_Handler</STRONG> (Thumb, 934 bytes, Stack size 40 bytes, stm32f1xx_hal_pcd.o(i.PCD_EP_ISR_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = PCD_EP_ISR_Handler &rArr; HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetupStageCallback
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DataOutStageCallback
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DataInStageCallback
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ReadPMA
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPStartXfer
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_DB_Transmit
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_DB_Receive
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[6e]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[c5]"></a>USBD_GetDescriptor</STRONG> (Thumb, 232 bytes, Stack size 24 bytes, usbd_ctlreq.o(i.USBD_GetDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[d0]"></a>USBD_SetConfig</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, usbd_ctlreq.o(i.USBD_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_SetConfig &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetClassConfig
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_ClrClassConfig
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[11]"></a>USBD_CUSTOM_HID_DataIn</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_DataIn))
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL>
<P><STRONG><a name="[12]"></a>USBD_CUSTOM_HID_DataOut</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_DataOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = USBD_CUSTOM_HID_DataOut &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL>
<P><STRONG><a name="[e]"></a>USBD_CUSTOM_HID_DeInit</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USBD_CUSTOM_HID_DeInit &rArr; USBD_LL_CloseEP &rArr; HAL_PCD_EP_Close &rArr; USB_DeactivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_static_free
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_CloseEP
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL>
<P><STRONG><a name="[10]"></a>USBD_CUSTOM_HID_EP0_RxReady</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_EP0_RxReady))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_CUSTOM_HID_EP0_RxReady
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL>
<P><STRONG><a name="[16]"></a>USBD_CUSTOM_HID_GetDeviceQualifierDesc</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_GetDeviceQualifierDesc))
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL>
<P><STRONG><a name="[14]"></a>USBD_CUSTOM_HID_GetFSCfgDesc</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_GetFSCfgDesc))
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL>
<P><STRONG><a name="[13]"></a>USBD_CUSTOM_HID_GetHSCfgDesc</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_GetHSCfgDesc))
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL>
<P><STRONG><a name="[15]"></a>USBD_CUSTOM_HID_GetOtherSpeedCfgDesc</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc))
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL>
<P><STRONG><a name="[d]"></a>USBD_CUSTOM_HID_Init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_CUSTOM_HID_Init &rArr; USBD_LL_OpenEP &rArr; HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_static_malloc
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL>
<P><STRONG><a name="[f]"></a>USBD_CUSTOM_HID_Setup</STRONG> (Thumb, 218 bytes, Stack size 24 bytes, usbd_customhid.o(i.USBD_CUSTOM_HID_Setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = USBD_CUSTOM_HID_Setup &rArr; USBD_CtlPrepareRx &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePMA
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlPrepareRx
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
