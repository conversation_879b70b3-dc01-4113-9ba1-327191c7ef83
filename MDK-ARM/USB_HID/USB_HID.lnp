--cpu Cortex-M3
"usb_hid\startup_stm32f103xb.o"
"usb_hid\main.o"
"usb_hid\gpio.o"
"usb_hid\stm32f1xx_it.o"
"usb_hid\stm32f1xx_hal_msp.o"
"usb_hid\usb_device.o"
"usb_hid\usbd_desc.o"
"usb_hid\usbd_custom_hid_if.o"
"usb_hid\usbd_conf.o"
"usb_hid\stm32f1xx_hal_gpio_ex.o"
"usb_hid\stm32f1xx_hal_pcd.o"
"usb_hid\stm32f1xx_hal_pcd_ex.o"
"usb_hid\stm32f1xx_ll_usb.o"
"usb_hid\stm32f1xx_hal.o"
"usb_hid\stm32f1xx_hal_rcc.o"
"usb_hid\stm32f1xx_hal_rcc_ex.o"
"usb_hid\stm32f1xx_hal_gpio.o"
"usb_hid\stm32f1xx_hal_dma.o"
"usb_hid\stm32f1xx_hal_cortex.o"
"usb_hid\stm32f1xx_hal_pwr.o"
"usb_hid\stm32f1xx_hal_flash.o"
"usb_hid\stm32f1xx_hal_flash_ex.o"
"usb_hid\stm32f1xx_hal_exti.o"
"usb_hid\system_stm32f1xx.o"
"usb_hid\usbd_core.o"
"usb_hid\usbd_ctlreq.o"
"usb_hid\usbd_ioreq.o"
"usb_hid\usbd_customhid.o"
--strict --scatter "USB_HID\USB_HID.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "USB_HID.map" -o USB_HID\USB_HID.axf