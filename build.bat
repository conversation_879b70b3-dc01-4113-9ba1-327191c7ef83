@echo off
echo USB HID项目编译脚本
echo =====================

REM 检查Keil MDK是否安装
if exist "C:\Keil_v5\UV4\UV4.exe" (
    set KEIL_PATH=C:\Keil_v5\UV4\UV4.exe
) else if exist "C:\Keil\UV4\UV4.exe" (
    set KEIL_PATH=C:\Keil\UV4\UV4.exe
) else (
    echo 错误: 未找到Keil MDK安装路径
    echo 请确保Keil MDK已正确安装
    pause
    exit /b 1
)

echo 找到Keil MDK: %KEIL_PATH%

REM 编译项目
echo 正在编译项目...
"%KEIL_PATH%" -b "MDK-ARM\USB_HID.uvprojx" -o "build_log.txt"

REM 检查编译结果
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 编译成功！
    echo 输出文件位置: MDK-ARM\USB_HID\USB_HID.hex
    echo.
    echo 下一步:
    echo 1. 使用ST-Link Utility或STM32CubeProgrammer烧录hex文件
    echo 2. 运行test_usb_wakeup.py进行测试
) else (
    echo.
    echo ✗ 编译失败！
    echo 请查看build_log.txt了解详细错误信息
    type build_log.txt
)

echo.
pause
