#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=NVIC
Mcu.IP1=RCC
Mcu.IP2=SYS
Mcu.IP3=USB
Mcu.IP4=USB_DEVICE
Mcu.IPNb=5
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PC13-TAMPER-RTC
Mcu.Pin1=PD0-OSC_IN
Mcu.Pin10=VP_USB_DEVICE_VS_USB_DEVICE_CUSTOM_HID_FS
Mcu.Pin2=PD1-OSC_OUT
Mcu.Pin3=PA0-WKUP
Mcu.Pin4=PA1
Mcu.Pin5=PA11
Mcu.Pin6=PA12
Mcu.Pin7=PA13
Mcu.Pin8=PA14
Mcu.Pin9=VP_SYS_VS_Systick
Mcu.PinsNb=11
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI0_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USB_LP_CAN1_RX0_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PA0-WKUP.GPIO_Label=KEY2
PA0-WKUP.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PA0-WKUP.GPIO_PuPd=GPIO_PULLUP
PA0-WKUP.Locked=true
PA0-WKUP.Signal=GPXTI0
PA1.GPIOParameters=GPIO_Speed,PinState
PA1.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA1.Locked=true
PA1.PinState=GPIO_PIN_SET
PA1.Signal=GPIO_Output
PA11.Mode=Device
PA11.Signal=USB_DM
PA12.Mode=Device
PA12.Signal=USB_DP
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PC13-TAMPER-RTC.GPIOParameters=PinState,GPIO_Label
PC13-TAMPER-RTC.GPIO_Label=LED_RED
PC13-TAMPER-RTC.Locked=true
PC13-TAMPER-RTC.PinState=GPIO_PIN_SET
PC13-TAMPER-RTC.Signal=GPIO_Output
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=USB_HID.ioc
ProjectManager.ProjectName=USB_HID
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_USB_DEVICE_Init-USB_DEVICE-false-HAL-false
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,USBPrescaler,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=48000000
RCC.USBPrescaler=RCC_USBCLKSOURCE_PLL_DIV1_5
RCC.VCOOutput2Freq_Value=8000000
SH.GPXTI0.0=GPIO_EXTI0
SH.GPXTI0.ConfNb=1
USB_DEVICE.CLASS_NAME_FS=CUSTOM_HID
USB_DEVICE.CUSTOM_HID_FS_BINTERVAL=0xa
USB_DEVICE.IPParameters=VirtualMode,VirtualModeFS,CLASS_NAME_FS,USBD_CUSTOM_HID_REPORT_DESC_SIZE,USBD_CUSTOMHID_OUTREPORT_BUF_SIZE,CUSTOM_HID_FS_BINTERVAL,VID,PID_CUSTOMHID_FS,MANUFACTURER_STRING
USB_DEVICE.MANUFACTURER_STRING=STM32USBHID
USB_DEVICE.PID_CUSTOMHID_FS=0x5750 
USB_DEVICE.USBD_CUSTOMHID_OUTREPORT_BUF_SIZE=64
USB_DEVICE.USBD_CUSTOM_HID_REPORT_DESC_SIZE=25
USB_DEVICE.VID=0x483
USB_DEVICE.VirtualMode=CustomHid
USB_DEVICE.VirtualModeFS=Custom_Hid_FS
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_USB_DEVICE_VS_USB_DEVICE_CUSTOM_HID_FS.Mode=CUSTOM_HID_FS
VP_USB_DEVICE_VS_USB_DEVICE_CUSTOM_HID_FS.Signal=USB_DEVICE_VS_USB_DEVICE_CUSTOM_HID_FS
board=custom
