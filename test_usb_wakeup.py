#!/usr/bin/env python3
"""
USB HID设备测试脚本
用于测试USB设备的挂起和唤醒功能
"""

import hid
import time
import sys

# USB设备的VID和PID
VID = 0x483
PID = 0x5750

def find_device():
    """查找USB HID设备"""
    try:
        device = hid.device()
        device.open(VID, PID)
        return device
    except Exception as e:
        print(f"无法打开设备: {e}")
        return None

def test_communication(device):
    """测试与设备的通信"""
    try:
        # 发送测试数据
        data = [0x01, 0x02, 0x03, 0x04] + [0x00] * 60
        device.write(data)
        print("发送数据成功")
        
        # 尝试读取数据
        device.set_nonblocking(1)
        response = device.read(64, timeout_ms=1000)
        if response:
            print(f"接收到数据: {response[:10]}...")
            return True
        else:
            print("未接收到数据")
            return False
    except Exception as e:
        print(f"通信错误: {e}")
        return False

def main():
    print("USB HID设备挂起/唤醒测试")
    print("=" * 40)
    
    # 查找设备
    print("正在查找设备...")
    device = find_device()
    if not device:
        print("未找到设备，请检查设备连接")
        return
    
    print(f"找到设备: VID={VID:04X}, PID={PID:04X}")
    
    try:
        # 初始通信测试
        print("\n1. 初始通信测试...")
        if test_communication(device):
            print("✓ 初始通信正常")
        else:
            print("✗ 初始通信失败")
        
        # 等待设备挂起
        print("\n2. 等待设备挂起...")
        print("请等待约3秒让设备进入挂起状态...")
        time.sleep(5)
        
        # 尝试唤醒设备
        print("\n3. 尝试唤醒设备...")
        print("现在请按下设备上的按键来唤醒设备...")
        
        # 监控设备状态
        for i in range(30):  # 等待30秒
            try:
                if test_communication(device):
                    print("✓ 设备唤醒成功，通信恢复正常")
                    break
                time.sleep(1)
                print(f"等待中... ({i+1}/30)")
            except Exception as e:
                print(f"测试过程中出错: {e}")
                break
        else:
            print("✗ 设备未能在30秒内恢复通信")
        
        # 连续测试
        print("\n4. 连续通信测试...")
        success_count = 0
        total_count = 10
        
        for i in range(total_count):
            if test_communication(device):
                success_count += 1
            time.sleep(0.5)
        
        print(f"连续测试结果: {success_count}/{total_count} 成功")
        
    finally:
        device.close()
        print("\n测试完成")

if __name__ == "__main__":
    try:
        import hid
    except ImportError:
        print("请安装hidapi库: pip install hidapi")
        sys.exit(1)
    
    main()
